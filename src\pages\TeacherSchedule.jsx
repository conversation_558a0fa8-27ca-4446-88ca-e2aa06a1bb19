import React, { useEffect, useState, useContext } from 'react';
import { Box, Text, useNavigate } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import LoadingIndicator from '../components/LoadingIndicator';

const TeacherSchedule = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [teacherClasses, setTeacherClasses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
    const [currentYear, setCurrentYear] = useState(new Date().getFullYear());

    // Fetch teacher classes
    useEffect(() => {
        const fetchTeacherClasses = async () => {
            if (!user) return;
            
            setLoading(true);
            try {
                const response = await authApi.get('/directory/teacher/classes');
                if (response.data.success) {
                    setTeacherClasses(response.data.data || []);
                }
            } catch (err) {
                console.error('Error fetching teacher classes:', err);
                setError('Lỗi khi tải lịch dạy');
            } finally {
                setLoading(false);
            }
        };

        if (user && (user.role === 'teacher' || user.role === 'TEACHER')) {
            fetchTeacherClasses();
        }
    }, [user]);

    // Check authentication
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && user.role !== 'teacher' && user.role !== 'TEACHER') {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Generate calendar for current month
    const generateCalendar = () => {
        const firstDay = new Date(currentYear, currentMonth, 1);
        const lastDay = new Date(currentYear, currentMonth + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday
        
        const calendar = [];
        const today = new Date();
        
        for (let week = 0; week < 6; week++) {
            const weekDays = [];
            for (let day = 0; day < 7; day++) {
                const currentDate = new Date(startDate);
                currentDate.setDate(startDate.getDate() + week * 7 + day);
                
                const isCurrentMonth = currentDate.getMonth() === currentMonth;
                const isToday = currentDate.toDateString() === today.toDateString();
                
                // Check if there are classes on this day
                const dayName = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][currentDate.getDay()];
                const hasClass = teacherClasses.some(cls => 
                    cls.teachingPeriods?.some(period => period.day === dayName)
                );
                
                weekDays.push({
                    date: currentDate.getDate(),
                    fullDate: new Date(currentDate),
                    isCurrentMonth,
                    isToday,
                    hasClass: hasClass && isCurrentMonth
                });
            }
            calendar.push(weekDays);
        }
        
        return calendar;
    };

    // Get classes for a specific day
    const getClassesForDay = (dayName) => {
        const classes = [];
        teacherClasses.forEach(cls => {
            cls.teachingPeriods?.forEach(period => {
                if (period.day === dayName) {
                    classes.push({
                        className: cls.name,
                        subject: period.subject,
                        period: period.periodNumber,
                        room: period.room
                    });
                }
            });
        });
        return classes.sort((a, b) => a.period - b.period);
    };

    const monthNames = [
        'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
        'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
    ];

    const weekDays = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];

    if (authLoading) {
        return (
            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
                <LoadingIndicator />
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Lịch dạy" 
                showBackButton={true} 
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />
            
            <Box style={{ padding: '15px' }}>
                {loading ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : error ? (
                    <Box style={{ textAlign: 'center', padding: '20px' }}>
                        <Text style={{ color: 'red' }}>{error}</Text>
                    </Box>
                ) : (
                    <>
                        {/* Month Navigation */}
                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px', backgroundColor: 'white', padding: '15px', borderRadius: '10px' }}>
                            <Text 
                                style={{ fontSize: '18px', cursor: 'pointer', color: '#0068ff' }}
                                onClick={() => {
                                    if (currentMonth === 0) {
                                        setCurrentMonth(11);
                                        setCurrentYear(currentYear - 1);
                                    } else {
                                        setCurrentMonth(currentMonth - 1);
                                    }
                                }}
                            >
                                ‹
                            </Text>
                            <Text bold size="large">
                                {monthNames[currentMonth]} {currentYear}
                            </Text>
                            <Text 
                                style={{ fontSize: '18px', cursor: 'pointer', color: '#0068ff' }}
                                onClick={() => {
                                    if (currentMonth === 11) {
                                        setCurrentMonth(0);
                                        setCurrentYear(currentYear + 1);
                                    } else {
                                        setCurrentMonth(currentMonth + 1);
                                    }
                                }}
                            >
                                ›
                            </Text>
                        </Box>

                        {/* Calendar */}
                        <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px', marginBottom: '20px' }}>
                            {/* Week days header */}
                            <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '5px', marginBottom: '10px' }}>
                                {weekDays.map((day, index) => (
                                    <Text key={index} style={{ textAlign: 'center', fontSize: '12px', color: '#666', fontWeight: 'bold' }}>
                                        {day}
                                    </Text>
                                ))}
                            </Box>
                            
                            {/* Calendar grid */}
                            {generateCalendar().map((week, weekIndex) => (
                                <Box key={weekIndex} style={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: '5px', marginBottom: '5px' }}>
                                    {week.map((day, dayIndex) => (
                                        <Box 
                                            key={dayIndex} 
                                            style={{ 
                                                minHeight: '40px',
                                                display: 'flex',
                                                flexDirection: 'column',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                borderRadius: '5px',
                                                backgroundColor: day.isToday ? '#0068ff' : (day.hasClass ? '#e8f0fe' : 'transparent'),
                                                color: day.isToday ? 'white' : (day.isCurrentMonth ? 'black' : '#ccc'),
                                                border: day.hasClass && !day.isToday ? '1px solid #0068ff' : 'none',
                                                position: 'relative'
                                            }}
                                        >
                                            <Text style={{ fontSize: '14px', fontWeight: day.isToday ? 'bold' : 'normal' }}>
                                                {day.date}
                                            </Text>
                                            {day.hasClass && (
                                                <Box style={{ 
                                                    width: '4px', 
                                                    height: '4px', 
                                                    borderRadius: '50%', 
                                                    backgroundColor: day.isToday ? 'white' : '#ff9500',
                                                    marginTop: '2px'
                                                }} />
                                            )}
                                        </Box>
                                    ))}
                                </Box>
                            ))}
                        </Box>

                        {/* Weekly Schedule Details */}
                        <Box style={{ backgroundColor: 'white', borderRadius: '10px', padding: '15px' }}>
                            <Text bold size="large" style={{ marginBottom: '15px' }}>
                                Lịch dạy trong tuần
                            </Text>
                            
                            {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((dayEn, index) => {
                                const dayVN = weekDays[index];
                                const dayClasses = getClassesForDay(dayEn);
                                
                                return (
                                    <Box key={dayEn} style={{ marginBottom: '15px', paddingBottom: '15px', borderBottom: index < 6 ? '1px solid #f0f0f0' : 'none' }}>
                                        <Text bold style={{ marginBottom: '10px', color: '#0068ff' }}>
                                            {dayVN}
                                        </Text>
                                        {dayClasses.length > 0 ? (
                                            dayClasses.map((cls, clsIndex) => (
                                                <Box key={clsIndex} style={{ 
                                                    backgroundColor: '#f9f9f9', 
                                                    padding: '10px', 
                                                    borderRadius: '5px', 
                                                    marginBottom: '5px',
                                                    display: 'flex',
                                                    justifyContent: 'space-between',
                                                    alignItems: 'center'
                                                }}>
                                                    <Box>
                                                        <Text bold style={{ fontSize: '14px' }}>
                                                            {cls.className} - {cls.subject}
                                                        </Text>
                                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                                            {cls.room}
                                                        </Text>
                                                    </Box>
                                                    <Text style={{ fontSize: '12px', color: '#0068ff', fontWeight: 'bold' }}>
                                                        Tiết {cls.period}
                                                    </Text>
                                                </Box>
                                            ))
                                        ) : (
                                            <Text style={{ fontSize: '14px', color: '#999', fontStyle: 'italic' }}>
                                                Không có lịch dạy
                                            </Text>
                                        )}
                                    </Box>
                                );
                            })}
                        </Box>
                    </>
                )}
            </Box>
            
            <BottomNavigationEdu />
        </Box>
    );
};

export default TeacherSchedule;
