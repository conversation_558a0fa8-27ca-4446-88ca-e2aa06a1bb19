import React, { useEffect, useState, useRef, useContext, useCallback } from 'react';
import { Box, Text, List, useNavigate, Modal } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import BottomNavigation from '../components/BottomNavigationEdu';
import Loading from '../components/Loading';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import HeaderSpacer from '../components/HeaderSpacer';
import LoadingIndicator from '../components/LoadingIndicator';

const AllAnnouncements = () => {
    const navigate = useNavigate();
    const { user, classId, loading: authLoading } = useContext(AuthContext);
    const [announcements, setAnnouncements] = useState([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const observerRef = useRef(null);
    const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
    const [announcementDetailVisible, setAnnouncementDetailVisible] = useState(false);

    // Kiểm tra user và redirect nếu chưa đăng nhập
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Mark announcement as read
    const markAsRead = useCallback(async (announcementId) => {
        try {
            await authApi.post(`/announcements/${announcementId}/read`);
            // Update local state
            setAnnouncements(prev =>
                prev.map(ann =>
                    ann.id === announcementId ? { ...ann, isRead: true } : ann
                )
            );
        } catch (err) {
            console.error('Error marking announcement as read:', err);
        }
    }, []);

    // Handle announcement click
    const handleAnnouncementClick = useCallback(async (announcement) => {
        // Mark as read immediately for better UX
        if (!announcement.isRead) {
            markAsRead(announcement.id);
        }

        // Then fetch and show detail
        try {
            const response = await authApi.get(`/announcements/${announcement.id}`);
            setSelectedAnnouncement(response.data);
            setAnnouncementDetailVisible(true);
        } catch (err) {
            console.error('Error fetching announcement detail:', err);
            // Fallback to show current data
            setSelectedAnnouncement(announcement);
            setAnnouncementDetailVisible(true);
        }
    }, [markAsRead]);

    // Fetch announcement detail (legacy - keep for compatibility)
    const fetchAnnouncementDetail = useCallback(async (announcementId) => {
        try {
            const response = await authApi.get(`/announcements/${announcementId}`);
            setSelectedAnnouncement(response.data);
            setAnnouncementDetailVisible(true);
            // Mark as read when viewing detail
            if (!response.data.isRead) {
                markAsRead(announcementId);
            }
        } catch (err) {
            console.error('Error fetching announcement detail:', err);
        }
    }, [markAsRead]);

    // Fetch announcements with pagination
    const fetchAnnouncements = useCallback(async (pageNum = 1, isLoadMore = false) => {
        if (!user) return;

        if (!isLoadMore) setLoading(true);

        try {
            const response = await authApi.get(`/announcements/recent?limit=10&page=${pageNum}`);

            let announcementsData = [];
            let totalPagesData = 1;

            if (response.data && Array.isArray(response.data)) {
                announcementsData = response.data;
                // Assume more pages if we get full limit
                totalPagesData = announcementsData.length === 10 ? pageNum + 1 : pageNum;
            } else if (response.data && response.data.data) {
                announcementsData = response.data.data;
                totalPagesData = response.data.totalPages || 1;
            }

            if (isLoadMore) {
                setAnnouncements(prev => [...prev, ...announcementsData]);
            } else {
                setAnnouncements(announcementsData);
            }

            setTotalPages(totalPagesData);
            setPage(pageNum);
        } catch (err) {
            console.error('Error fetching announcements:', err);
        } finally {
            setLoading(false);
        }
    }, [user]);

    // Initial load
    useEffect(() => {
        if (user) {
            fetchAnnouncements(1, false);
        }
    }, [user, fetchAnnouncements]);

    // Load more announcements
    const handleLoadMore = () => {
        if (page < totalPages && !loading) {
            fetchAnnouncements(page + 1, true);
        }
    };

    // Infinite scroll
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && page < totalPages && !loading) {
                    handleLoadMore();
                }
            },
            { threshold: 0.1 }
        );

        if (observerRef.current) {
            observer.observe(observerRef.current);
        }

        return () => {
            if (observerRef.current) {
                observer.unobserve(observerRef.current);
            }
        };
    }, [page, totalPages, loading]);

    return (
        <Box style={{ backgroundColor: '#f5f5f5', minHeight: '100vh', position: 'relative' }}>
            <HeaderEdu />
            <HeaderSpacer />
            {authLoading ? (
                <Text>Đang tải thông tin...</Text>
            ) : (
                <Box style={{ padding: '15px' }}>
                    <Text bold size="xLarge" style={{ marginBottom: '15px' }}>
                        Tất cả thông báo
                    </Text>
                    {loading && announcements.length === 0 ? (
                        <Box style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
                            <LoadingIndicator />
                        </Box>
                    ) : (
                        <List style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            {announcements.map((item) => (
                                <Box
                                    key={item.id}
                                    style={{
                                        display: 'flex',
                                        backgroundColor: item.isRead ? '#f9f9f9' : '#f0f8ff',
                                        borderRadius: '8px',
                                        padding: '12px',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                        cursor: 'pointer',
                                        borderLeft: item.isRead ? 'none' : '4px solid #0068ff'
                                    }}
                                    onClick={() => handleAnnouncementClick(item)}
                                >
                                    <Box
                                        style={{
                                            width: '40px',
                                            height: '40px',
                                            borderRadius: '50%',
                                            backgroundColor: '#e8f0fe',
                                            color: '#0068ff',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginRight: '12px',
                                            fontSize: '20px'
                                        }}
                                    >
                                        📢
                                    </Box>
                                    <Box style={{ flex: 1 }}>
                                        <Text style={{
                                            lineHeight: 1.4,
                                            marginBottom: '5px',
                                            fontWeight: item.isRead ? 'normal' : 'bold',
                                            color: item.isRead ? '#333' : '#000'
                                        }}>
                                            {item.title || item.content?.substring(0, 80) + (item.content?.length > 80 ? '...' : '')}
                                        </Text>
                                        <Text style={{
                                            fontSize: '12px',
                                            color: item.isRead ? '#888' : '#666',
                                            fontWeight: item.isRead ? 'normal' : '500'
                                        }}>
                                            {item.sender?.name} • {formatDistanceToNow(new Date(item.createdAt), { locale: vi, addSuffix: true })}
                                        </Text>
                                    </Box>
                                    {!item.isRead && (
                                        <Box style={{ width: '8px', height: '8px', borderRadius: '50%', backgroundColor: '#0068ff', alignSelf: 'center' }} />
                                    )}
                                </Box>
                            ))}

                            {announcements.length === 0 && !loading && (
                                <Text style={{ textAlign: 'center', color: '#888', padding: '40px' }}>Chưa có thông báo nào</Text>
                            )}

                            {page < totalPages && (
                                <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                                    <Box
                                        style={{
                                            backgroundColor: '#0068ff',
                                            color: 'white',
                                            padding: '10px 20px',
                                            borderRadius: '6px',
                                            cursor: 'pointer',
                                            fontSize: '14px'
                                        }}
                                        onClick={handleLoadMore}
                                    >
                                        {loading ? 'Đang tải...' : 'Xem thêm'}
                                    </Box>
                                </Box>
                            )}
                        </List>
                    )}
                    <div ref={observerRef} style={{ height: '20px' }} />
                </Box>
            )}

            {/* Announcement Detail Modal */}
            <Modal
                visible={announcementDetailVisible}
                title="Chi tiết thông báo"
                onClose={() => {
                    setAnnouncementDetailVisible(false);
                    setSelectedAnnouncement(null);
                }}
                actions={[
                    { text: 'Đóng', close: true }
                ]}
            >
                {selectedAnnouncement && (
                    <Box p={4}>
                        <Box style={{ marginBottom: '15px' }}>
                            <Text bold size="large" style={{ marginBottom: '8px' }}>
                                {selectedAnnouncement.title || 'Thông báo'}
                            </Text>
                            <Box flex alignItems="center" style={{ marginBottom: '10px' }}>
                                <Text style={{ fontSize: '14px', color: '#666' }}>
                                    Từ: <Text bold>{selectedAnnouncement.sender?.name}</Text>
                                </Text>
                                <Text style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>
                                    {formatDistanceToNow(new Date(selectedAnnouncement.createdAt), { locale: vi, addSuffix: true })}
                                </Text>
                            </Box>
                            {selectedAnnouncement.type && (
                                <Box style={{
                                    backgroundColor: '#e8f0fe',
                                    color: '#0068ff',
                                    padding: '4px 8px',
                                    borderRadius: '4px',
                                    fontSize: '12px',
                                    display: 'inline-block',
                                    marginBottom: '10px'
                                }}>
                                    {selectedAnnouncement.type === 'teacher_to_student' && 'Giáo viên gửi học sinh'}
                                    {selectedAnnouncement.type === 'head_to_teacher' && 'Tổ trưởng gửi giáo viên'}
                                    {selectedAnnouncement.type === 'principal_to_teacher' && 'Hiệu trưởng gửi giáo viên'}
                                    {selectedAnnouncement.type === 'admin_to_all' && 'Ban giám hiệu gửi toàn trường'}
                                </Box>
                            )}
                        </Box>
                        <Box style={{
                            backgroundColor: '#f9f9f9',
                            padding: '15px',
                            borderRadius: '8px',
                            lineHeight: 1.6
                        }}>
                            <Text>{selectedAnnouncement.content}</Text>
                        </Box>
                        {selectedAnnouncement.zaloConfig?.enabled && (
                            <Box style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f0f8ff', borderRadius: '6px' }}>
                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                    📱 Đã gửi qua Zalo: {selectedAnnouncement.zaloConfig.groupName}
                                    {selectedAnnouncement.zaloConfig.sentAt && (
                                        <Text> • {formatDistanceToNow(new Date(selectedAnnouncement.zaloConfig.sentAt), { locale: vi, addSuffix: true })}</Text>
                                    )}
                                </Text>
                            </Box>
                        )}
                    </Box>
                )}
            </Modal>

            <BottomNavigation />
        </Box>
    );
};

export default AllAnnouncements;