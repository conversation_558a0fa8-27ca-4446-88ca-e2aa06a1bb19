import React, { useEffect, useState, useRef, useContext, useCallback } from 'react';
import { Box, Text, List, useNavigate, Modal, Select, Input, Button } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import BottomNavigation from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import HeaderSpacer from '../components/HeaderSpacer';
import LoadingIndicator from '../components/LoadingIndicator';

const { Option } = Select;

const AllAnnouncements = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [announcements, setAnnouncements] = useState([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalAnnouncements, setTotalAnnouncements] = useState(0);
    const observerRef = useRef(null);
    const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
    const [announcementDetailVisible, setAnnouncementDetailVisible] = useState(false);

    // Read status modal states
    const [readStatusVisible, setReadStatusVisible] = useState(false);
    const [readStatusData, setReadStatusData] = useState(null);
    const [readStatusLoading, setReadStatusLoading] = useState(false);
    const [readStatusTab, setReadStatusTab] = useState('read'); // 'read' or 'unread'

    // Filter and search states
    const [mode, setMode] = useState('received'); // 'received', 'sent', 'all'
    const [searchQuery, setSearchQuery] = useState('');
    const [filterType, setFilterType] = useState('all'); // 'all', 'teacher_to_student', etc.
    const [filterStatus, setFilterStatus] = useState('all'); // 'all', 'sent', 'scheduled', etc.
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [showFilters, setShowFilters] = useState(false);

    // Statistics and config
    const [statistics, setStatistics] = useState(null);
    const [announcementConfig, setAnnouncementConfig] = useState(null);
    const [bookmarks, setBookmarks] = useState(new Set());

    // Dashboard data
    const [dashboardData, setDashboardData] = useState(null);

    // Kiểm tra user và redirect nếu chưa đăng nhập
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Mark announcement as read
    const markAsRead = useCallback(async (announcementId) => {
        try {
            await authApi.post(`/announcements/${announcementId}/read`);
            // Update local state
            setAnnouncements(prev =>
                prev.map(ann =>
                    ann.id === announcementId ? { ...ann, isRead: true } : ann
                )
            );
        } catch (err) {
            console.error('Error marking announcement as read:', err);
        }
    }, []);

    // Handle announcement click
    const handleAnnouncementClick = useCallback(async (announcement) => {
        // Mark as read immediately for better UX
        if (!announcement.isRead) {
            markAsRead(announcement.id);
        }

        // Then fetch and show detail
        try {
            const response = await authApi.get(`/announcements/${announcement.id}`);
            setSelectedAnnouncement(response.data);
            setAnnouncementDetailVisible(true);
        } catch (err) {
            console.error('Error fetching announcement detail:', err);
            // Fallback to show current data
            setSelectedAnnouncement(announcement);
            setAnnouncementDetailVisible(true);
        }
    }, [markAsRead]);

    // Fetch announcement detail (legacy - keep for compatibility)
    const fetchAnnouncementDetail = useCallback(async (announcementId) => {
        try {
            const response = await authApi.get(`/announcements/${announcementId}`);
            setSelectedAnnouncement(response.data);
            setAnnouncementDetailVisible(true);
            // Mark as read when viewing detail
            if (!response.data.isRead) {
                markAsRead(announcementId);
            }
        } catch (err) {
            console.error('Error fetching announcement detail:', err);
        }
    }, [markAsRead]);

    // Fetch announcements with advanced filtering
    const fetchAnnouncements = useCallback(async (pageNum = 1, isLoadMore = false) => {
        if (!user) return;

        if (!isLoadMore) setLoading(true);

        try {
            let apiUrl;
            let params = new URLSearchParams({
                page: pageNum.toString(),
                limit: '10'
            });

            // Determine API endpoint based on search query and filters
            if (searchQuery.trim()) {
                // Use search API
                apiUrl = '/announcements/search';
                params.append('q', searchQuery.trim());
                params.append('mode', mode);
                if (filterType !== 'all') params.append('type', filterType);
                if (filterStatus !== 'all') params.append('status', filterStatus);
                if (startDate) params.append('startDate', startDate);
                if (endDate) params.append('endDate', endDate);
            } else {
                // Use regular API with mode
                if (mode === 'sent') {
                    apiUrl = '/announcements/sent';
                    if (filterType !== 'all') params.append('type', filterType);
                    if (filterStatus !== 'all') params.append('status', filterStatus);
                } else {
                    apiUrl = '/announcements';
                    params.append('mode', mode);
                }
            }

            const response = await authApi.get(`${apiUrl}?${params.toString()}`);

            let announcementsData = [];
            let totalPagesData = 1;
            let totalAnnouncementsData = 0;

            if (response.data) {
                if (response.data.announcements) {
                    // New API format with pagination info
                    announcementsData = response.data.announcements;
                    totalPagesData = response.data.totalPages || 1;
                    totalAnnouncementsData = response.data.totalAnnouncements || 0;
                } else if (Array.isArray(response.data)) {
                    // Legacy format
                    announcementsData = response.data;
                    totalPagesData = announcementsData.length === 10 ? pageNum + 1 : pageNum;
                    totalAnnouncementsData = announcementsData.length;
                }
            }

            if (isLoadMore) {
                setAnnouncements(prev => [...prev, ...announcementsData]);
            } else {
                setAnnouncements(announcementsData);
            }

            setTotalPages(totalPagesData);
            setTotalAnnouncements(totalAnnouncementsData);
            setPage(pageNum);
        } catch (err) {
            console.error('Error fetching announcements:', err);
        } finally {
            setLoading(false);
        }
    }, [user, mode, searchQuery, filterType, filterStatus, startDate, endDate]);

    // Fetch statistics
    const fetchStatistics = useCallback(async () => {
        if (!user) return;

        try {
            let params = new URLSearchParams({ mode });
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);

            const response = await authApi.get(`/announcements/stats?${params.toString()}`);
            setStatistics(response.data);
        } catch (err) {
            console.error('Error fetching statistics:', err);
        }
    }, [user, mode, startDate, endDate]);

    // Fetch announcement config
    const fetchConfig = useCallback(async () => {
        if (!user) return;

        try {
            const response = await authApi.get('/announcements/config');
            setAnnouncementConfig(response.data);
        } catch (err) {
            console.error('Error fetching config:', err);
        }
    }, [user]);

    // Bookmark functions
    const toggleBookmark = useCallback(async (announcementId, note = '') => {
        try {
            const response = await authApi.post(`/announcements/${announcementId}/bookmark`, {
                note
            });

            if (response.data.bookmarked) {
                setBookmarks(prev => new Set([...prev, announcementId]));
            } else {
                setBookmarks(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(announcementId);
                    return newSet;
                });
            }
        } catch (err) {
            console.error('Error toggling bookmark:', err);
        }
    }, []);

    // Fetch bookmarks
    const fetchBookmarks = useCallback(async () => {
        if (!user) return;

        try {
            const response = await authApi.get('/announcements/bookmarks?page=1&limit=100');
            const bookmarkIds = response.data.announcements?.map(ann => ann._id) || [];
            setBookmarks(new Set(bookmarkIds));
        } catch (err) {
            console.error('Error fetching bookmarks:', err);
        }
    }, [user]);

    // Fetch read status for sent announcements
    const fetchReadStatus = useCallback(async (announcementId) => {
        setReadStatusLoading(true);
        try {
            const response = await authApi.get(`/announcements/${announcementId}/read-status`);
            if (response.data.success) {
                setReadStatusData(response.data.data);
                setReadStatusVisible(true);
            }
        } catch (err) {
            console.error('Error fetching read status:', err);
        } finally {
            setReadStatusLoading(false);
        }
    }, []);

    // Load more announcements
    const handleLoadMore = () => {
        if (page < totalPages && !loading) {
            fetchAnnouncements(page + 1, true);
        }
    };

    // Initial load and fetch additional data
    useEffect(() => {
        if (user) {
            fetchAnnouncements(1, false);
            fetchStatistics();
            fetchConfig();
            fetchBookmarks();
        }
    }, [user, fetchAnnouncements, fetchStatistics, fetchConfig, fetchBookmarks]);

    // Infinite scroll
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                if (entries[0].isIntersecting && page < totalPages && !loading) {
                    handleLoadMore();
                }
            },
            { threshold: 0.1 }
        );

        if (observerRef.current) {
            observer.observe(observerRef.current);
        }

        return () => {
            if (observerRef.current) {
                observer.unobserve(observerRef.current);
            }
        };
    }, [page, totalPages, loading]);

    return (
        <Box style={{ backgroundColor: '#f5f5f5', minHeight: '100vh', position: 'relative', paddingBottom: '80px' }}>
            <HeaderEdu />
            <HeaderSpacer />
            {authLoading ? (
                <Text>Đang tải thông tin...</Text>
            ) : (
                <>
                    {/* Mode Tabs */}
                    <Box style={{ backgroundColor: 'white', margin: '15px', borderRadius: '8px', padding: '10px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                        <Box style={{ display: 'flex', gap: '5px' }}>
                            {[
                                { key: 'received', label: 'Nhận được', icon: '📥' },
                                { key: 'sent', label: 'Đã gửi', icon: '📤' },
                                { key: 'all', label: 'Tất cả', icon: '📋' }
                            ].map((tab) => (
                                <Box
                                    key={tab.key}
                                    style={{
                                        flex: 1,
                                        padding: '10px',
                                        borderRadius: '6px',
                                        backgroundColor: mode === tab.key ? '#0068ff' : '#f5f5f5',
                                        color: mode === tab.key ? 'white' : '#666',
                                        textAlign: 'center',
                                        cursor: 'pointer',
                                        fontSize: '14px',
                                        fontWeight: mode === tab.key ? 'bold' : 'normal'
                                    }}
                                    onClick={() => {
                                        setMode(tab.key);
                                        setPage(1);
                                        setAnnouncements([]);
                                    }}
                                >
                                    <Text>{tab.icon} {tab.label}</Text>
                                </Box>
                            ))}
                        </Box>
                    </Box>

                    {/* Search and Filters */}
                    <Box style={{ backgroundColor: 'white', margin: '15px', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                        {/* Search Bar */}
                        <Box style={{ marginBottom: '10px' }}>
                            <input
                                type="text"
                                placeholder="Tìm kiếm thông báo..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '10px',
                                    border: '1px solid #ddd',
                                    borderRadius: '6px',
                                    fontSize: '14px'
                                }}
                            />
                        </Box>

                        {/* Filter Toggle */}
                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                            <Text style={{ fontSize: '14px', fontWeight: 'bold' }}>Bộ lọc nâng cao</Text>
                            <Box
                                style={{
                                    cursor: 'pointer',
                                    padding: '5px 10px',
                                    backgroundColor: showFilters ? '#0068ff' : '#f5f5f5',
                                    color: showFilters ? 'white' : '#666',
                                    borderRadius: '4px',
                                    fontSize: '12px'
                                }}
                                onClick={() => setShowFilters(!showFilters)}
                            >
                                {showFilters ? '🔼 Ẩn' : '🔽 Hiện'}
                            </Box>
                        </Box>

                        {/* Advanced Filters */}
                        {showFilters && (
                            <Box style={{
                                display: 'grid',
                                gridTemplateColumns: window.innerWidth < 480 ? '1fr' : 'repeat(2, 1fr)',
                                gap: '10px',
                                marginBottom: '10px'
                            }}>
                                <Box>
                                    <Text style={{ fontSize: '12px', marginBottom: '5px' }}>Loại thông báo:</Text>
                                    <Select
                                        value={filterType}
                                        onChange={(value) => setFilterType(value)}
                                        style={{ width: '100%' }}
                                    >
                                        <Option value="all" title="Tất cả" />
                                        <Option value="teacher_to_student" title="GV → HS" />
                                        <Option value="head_to_teacher" title="Tổ trưởng → GV" />
                                        <Option value="principal_to_teacher" title="Hiệu trưởng → GV" />
                                        <Option value="admin_to_all" title="BGH → Toàn trường" />
                                    </Select>
                                </Box>
                                <Box>
                                    <Text style={{ fontSize: '12px', marginBottom: '5px' }}>Trạng thái:</Text>
                                    <Select
                                        value={filterStatus}
                                        onChange={(value) => setFilterStatus(value)}
                                        style={{ width: '100%' }}
                                    >
                                        <Option value="all" title="Tất cả" />
                                        <Option value="sent" title="Đã gửi" />
                                        <Option value="scheduled" title="Đã lên lịch" />
                                        <Option value="draft" title="Bản nháp" />
                                    </Select>
                                </Box>
                                <Box>
                                    <Text style={{ fontSize: '12px', marginBottom: '5px' }}>Từ ngày:</Text>
                                    <input
                                        type="date"
                                        value={startDate}
                                        onChange={(e) => setStartDate(e.target.value)}
                                        style={{
                                            width: '100%',
                                            padding: '8px',
                                            border: '1px solid #ddd',
                                            borderRadius: '4px',
                                            fontSize: '12px'
                                        }}
                                    />
                                </Box>
                                <Box>
                                    <Text style={{ fontSize: '12px', marginBottom: '5px' }}>Đến ngày:</Text>
                                    <input
                                        type="date"
                                        value={endDate}
                                        onChange={(e) => setEndDate(e.target.value)}
                                        style={{
                                            width: '100%',
                                            padding: '8px',
                                            border: '1px solid #ddd',
                                            borderRadius: '4px',
                                            fontSize: '12px'
                                        }}
                                    />
                                </Box>
                            </Box>
                        )}

                        {/* Apply Filters Button */}
                        <Box
                            style={{
                                backgroundColor: '#0068ff',
                                color: 'white',
                                padding: '10px',
                                borderRadius: '6px',
                                textAlign: 'center',
                                cursor: 'pointer',
                                fontSize: '14px',
                                fontWeight: 'bold'
                            }}
                            onClick={() => {
                                setPage(1);
                                setAnnouncements([]);
                                fetchAnnouncements(1, false);
                            }}
                        >
                            🔍 Tìm kiếm & Lọc
                        </Box>
                    </Box>

                    {/* Statistics Summary */}
                    {statistics && (
                        <Box style={{ backgroundColor: 'white', margin: '15px', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                            <Text bold style={{ marginBottom: '10px' }}>📊 Thống kê</Text>
                            <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '10px', marginBottom: '10px' }}>
                                <Box style={{ textAlign: 'center', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
                                    <Text style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>
                                        {statistics.total || 0}
                                    </Text>
                                    <Text style={{ fontSize: '11px', color: '#666' }}>Tổng số</Text>
                                </Box>
                                {statistics.byStatus?.slice(0, 2).map((stat, idx) => (
                                    <Box key={idx} style={{ textAlign: 'center', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
                                        <Text style={{ fontSize: '18px', fontWeight: 'bold', color: '#4CAF50' }}>
                                            {stat.count}
                                        </Text>
                                        <Text style={{ fontSize: '11px', color: '#666' }}>{stat.statusLabel}</Text>
                                    </Box>
                                ))}
                            </Box>
                        </Box>
                    )}

                    <Box style={{ padding: '15px' }}>
                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                            <Text bold size="large">
                                {mode === 'received' ? 'Thông báo nhận được' :
                                 mode === 'sent' ? 'Thông báo đã gửi' : 'Tất cả thông báo'}
                                {totalAnnouncements > 0 && ` (${totalAnnouncements})`}
                            </Text>
                            <Box
                                style={{
                                    cursor: 'pointer',
                                    padding: '8px',
                                    borderRadius: '50%',
                                    backgroundColor: '#f0f0f0',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                }}
                                onClick={() => {
                                    setPage(1);
                                    setAnnouncements([]);
                                    fetchAnnouncements(1, false);
                                    fetchStatistics();
                                }}
                            >
                                <Text style={{ fontSize: '16px' }}>🔄</Text>
                            </Box>
                        </Box>
                    {loading && announcements.length === 0 ? (
                        <Box style={{ display: 'flex', justifyContent: 'center', padding: '40px' }}>
                            <LoadingIndicator />
                        </Box>
                    ) : (
                        <List style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            {announcements.map((item) => (
                                <Box
                                    key={item.id}
                                    style={{
                                        display: 'flex',
                                        backgroundColor: item.isRead ? '#f9f9f9' : '#f0f8ff',
                                        borderRadius: '8px',
                                        padding: '12px',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                        cursor: 'pointer',
                                        borderLeft: item.isRead ? 'none' : '4px solid #0068ff'
                                    }}
                                    onClick={() => handleAnnouncementClick(item)}
                                >
                                    <Box
                                        style={{
                                            width: '40px',
                                            height: '40px',
                                            borderRadius: '50%',
                                            backgroundColor: '#e8f0fe',
                                            color: '#0068ff',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginRight: '12px',
                                            fontSize: '20px'
                                        }}
                                    >
                                        📢
                                    </Box>
                                    <Box style={{ flex: 1 }}>
                                        <Text style={{
                                            lineHeight: 1.4,
                                            marginBottom: '5px',
                                            fontWeight: (mode === 'received' && !item.isRead) ? 'bold' : 'normal',
                                            color: (mode === 'received' && !item.isRead) ? '#000' : '#333'
                                        }}>
                                            {item.title || item.content?.substring(0, 80) + (item.content?.length > 80 ? '...' : '')}
                                        </Text>
                                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                            <Text style={{
                                                fontSize: '12px',
                                                color: (mode === 'received' && !item.isRead) ? '#666' : '#888',
                                                fontWeight: (mode === 'received' && !item.isRead) ? '500' : 'normal'
                                            }}>
                                                {item.sender?.name} • {formatDistanceToNow(new Date(item.createdAt), { locale: vi, addSuffix: true })}
                                            </Text>
                                            {/* Type and Status Labels */}
                                            <Box style={{ display: 'flex', gap: '4px' }}>
                                                {item.typeLabel && (
                                                    <Box style={{
                                                        backgroundColor: '#e8f0fe',
                                                        color: '#0068ff',
                                                        padding: '2px 6px',
                                                        borderRadius: '3px',
                                                        fontSize: '10px'
                                                    }}>
                                                        {item.typeLabel}
                                                    </Box>
                                                )}
                                                {item.statusLabel && (
                                                    <Box style={{
                                                        backgroundColor: item.status === 'sent' ? '#e8f5e8' : '#fff3e0',
                                                        color: item.status === 'sent' ? '#2e7d32' : '#f57c00',
                                                        padding: '2px 6px',
                                                        borderRadius: '3px',
                                                        fontSize: '10px'
                                                    }}>
                                                        {item.statusLabel}
                                                    </Box>
                                                )}
                                            </Box>
                                        </Box>

                                        {/* Read Stats for sent announcements */}
                                        {mode === 'sent' && item.readStats && (
                                            <Box style={{ marginTop: '5px', padding: '5px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
                                                <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                    <Box
                                                        style={{
                                                            cursor: 'pointer',
                                                            padding: '2px 6px',
                                                            backgroundColor: '#0068ff',
                                                            color: 'white',
                                                            borderRadius: '3px',
                                                            fontSize: '10px'
                                                        }}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            fetchReadStatus(item._id || item.id);
                                                        }}
                                                    >
                                                        👥 Chi tiết
                                                    </Box>
                                                </Box>
                                            </Box>
                                        )}
                                    </Box>
                                    {/* Action buttons */}
                                    <Box style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '5px' }}>
                                        {/* Bookmark button */}
                                        <Box
                                            style={{
                                                cursor: 'pointer',
                                                padding: '4px',
                                                borderRadius: '50%',
                                                backgroundColor: bookmarks.has(item._id || item.id) ? '#FFD700' : '#f0f0f0',
                                                fontSize: '12px'
                                            }}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                toggleBookmark(item._id || item.id);
                                            }}
                                        >
                                            {bookmarks.has(item._id || item.id) ? '⭐' : '☆'}
                                        </Box>

                                        {/* Unread indicator */}
                                        {!item.isRead && (
                                            <Box style={{ width: '8px', height: '8px', borderRadius: '50%', backgroundColor: '#0068ff' }} />
                                        )}
                                    </Box>
                                </Box>
                            ))}

                            {announcements.length === 0 && !loading && (
                                <Text style={{ textAlign: 'center', color: '#888', padding: '40px' }}>Chưa có thông báo nào</Text>
                            )}

                            {page < totalPages && (
                                <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                                    <Box
                                        style={{
                                            backgroundColor: '#0068ff',
                                            color: 'white',
                                            padding: '10px 20px',
                                            borderRadius: '6px',
                                            cursor: 'pointer',
                                            fontSize: '14px'
                                        }}
                                        onClick={handleLoadMore}
                                    >
                                        {loading ? 'Đang tải...' : 'Xem thêm'}
                                    </Box>
                                </Box>
                            )}
                        </List>
                    )}
                    <div ref={observerRef} style={{ height: '20px' }} />
                </Box>
                </>
            )}

            {/* Announcement Detail Modal */}
            <Modal
                visible={announcementDetailVisible}
                title="Chi tiết thông báo"
                onClose={() => {
                    setAnnouncementDetailVisible(false);
                    setSelectedAnnouncement(null);
                }}
                actions={[
                    {
                        text: bookmarks.has(selectedAnnouncement?._id || selectedAnnouncement?.id) ? '⭐ Đã lưu' : '☆ Lưu',
                        onClick: () => {
                            if (selectedAnnouncement) {
                                toggleBookmark(selectedAnnouncement._id || selectedAnnouncement.id);
                            }
                        }
                    },
                    ...(mode === 'sent' && selectedAnnouncement?.readStats ? [{
                        text: '👥 Xem ai đã đọc',
                        onClick: () => {
                            if (selectedAnnouncement) {
                                setAnnouncementDetailVisible(false);
                                fetchReadStatus(selectedAnnouncement._id || selectedAnnouncement.id);
                            }
                        }
                    }] : []),
                    { text: 'Đóng', close: true }
                ]}
            >
                {selectedAnnouncement && (
                    <Box p={4}>
                        <Box style={{ marginBottom: '15px' }}>
                            <Text bold size="large" style={{ marginBottom: '8px' }}>
                                {selectedAnnouncement.title || 'Thông báo'}
                            </Text>
                            <Box flex alignItems="center" style={{ marginBottom: '10px' }}>
                                <Text style={{ fontSize: '14px', color: '#666' }}>
                                    Từ: <Text bold>{selectedAnnouncement.sender?.name}</Text>
                                </Text>
                                <Text style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>
                                    {formatDistanceToNow(new Date(selectedAnnouncement.createdAt), { locale: vi, addSuffix: true })}
                                </Text>
                            </Box>
                            {selectedAnnouncement.type && (
                                <Box style={{
                                    backgroundColor: '#e8f0fe',
                                    color: '#0068ff',
                                    padding: '4px 8px',
                                    borderRadius: '4px',
                                    fontSize: '12px',
                                    display: 'inline-block',
                                    marginBottom: '10px'
                                }}>
                                    {selectedAnnouncement.type === 'teacher_to_student' && 'Giáo viên gửi học sinh'}
                                    {selectedAnnouncement.type === 'head_to_teacher' && 'Tổ trưởng gửi giáo viên'}
                                    {selectedAnnouncement.type === 'principal_to_teacher' && 'Hiệu trưởng gửi giáo viên'}
                                    {selectedAnnouncement.type === 'admin_to_all' && 'Ban giám hiệu gửi toàn trường'}
                                </Box>
                            )}
                        </Box>
                        <Box style={{
                            backgroundColor: '#f9f9f9',
                            padding: '15px',
                            borderRadius: '8px',
                            lineHeight: 1.6
                        }}>
                            <Text>{selectedAnnouncement.content}</Text>
                        </Box>
                        {selectedAnnouncement.zaloConfig?.enabled && (
                            <Box style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f0f8ff', borderRadius: '6px' }}>
                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                    📱 Đã gửi qua Zalo: {selectedAnnouncement.zaloConfig.groupName}
                                    {selectedAnnouncement.zaloConfig.sentAt && (
                                        <Text> • {formatDistanceToNow(new Date(selectedAnnouncement.zaloConfig.sentAt), { locale: vi, addSuffix: true })}</Text>
                                    )}
                                </Text>
                            </Box>
                        )}
                    </Box>
                )}
            </Modal>

            {/* Read Status Modal */}
            <Modal
                visible={readStatusVisible}
                title="Trạng thái đọc thông báo"
                onClose={() => {
                    setReadStatusVisible(false);
                    setReadStatusData(null);
                    setReadStatusTab('read');
                }}
                actions={[
                    { text: 'Đóng', close: true }
                ]}
            >
                {readStatusData && (
                    <Box p={4}>
                        {/* Announcement Info */}
                        <Box style={{ marginBottom: '15px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
                            <Text bold style={{ marginBottom: '5px' }}>
                                {readStatusData.announcement.title}
                            </Text>
                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                {readStatusData.announcement.typeLabel} • {new Date(readStatusData.announcement.createdAt).toLocaleDateString('vi-VN')}
                            </Text>
                        </Box>

                        {/* Statistics Summary */}
                        <Box style={{ marginBottom: '15px' }}>
                            <Box style={{
                                display: 'grid',
                                gridTemplateColumns: window.innerWidth < 480 ? 'repeat(3, 1fr)' : 'repeat(3, 1fr)',
                                gap: '8px'
                            }}>
                                <Box style={{ textAlign: 'center', padding: '10px', backgroundColor: '#e8f5e8', borderRadius: '6px' }}>
                                    <Text style={{ fontSize: '18px', fontWeight: 'bold', color: '#4CAF50' }}>
                                        {readStatusData.stats.totalRead}
                                    </Text>
                                    <Text style={{ fontSize: '11px', color: '#666' }}>Đã đọc</Text>
                                </Box>
                                <Box style={{ textAlign: 'center', padding: '10px', backgroundColor: '#ffeaea', borderRadius: '6px' }}>
                                    <Text style={{ fontSize: '18px', fontWeight: 'bold', color: '#F44336' }}>
                                        {readStatusData.stats.totalUnread}
                                    </Text>
                                    <Text style={{ fontSize: '11px', color: '#666' }}>Chưa đọc</Text>
                                </Box>
                                <Box style={{ textAlign: 'center', padding: '10px', backgroundColor: '#f0f8ff', borderRadius: '6px' }}>
                                    <Text style={{ fontSize: '18px', fontWeight: 'bold', color: '#0068ff' }}>
                                        {readStatusData.stats.readPercentage}%
                                    </Text>
                                    <Text style={{ fontSize: '11px', color: '#666' }}>Tỷ lệ</Text>
                                </Box>
                            </Box>
                        </Box>

                        {/* Tab Navigation */}
                        <Box style={{ display: 'flex', marginBottom: '15px', backgroundColor: '#f5f5f5', borderRadius: '6px', padding: '2px' }}>
                            <Box
                                style={{
                                    flex: 1,
                                    padding: '8px',
                                    borderRadius: '4px',
                                    backgroundColor: readStatusTab === 'read' ? '#4CAF50' : 'transparent',
                                    color: readStatusTab === 'read' ? 'white' : '#666',
                                    textAlign: 'center',
                                    cursor: 'pointer',
                                    fontSize: '14px',
                                    fontWeight: readStatusTab === 'read' ? 'bold' : 'normal'
                                }}
                                onClick={() => setReadStatusTab('read')}
                            >
                                ✅ Đã đọc ({readStatusData.stats.totalRead})
                            </Box>
                            <Box
                                style={{
                                    flex: 1,
                                    padding: '8px',
                                    borderRadius: '4px',
                                    backgroundColor: readStatusTab === 'unread' ? '#F44336' : 'transparent',
                                    color: readStatusTab === 'unread' ? 'white' : '#666',
                                    textAlign: 'center',
                                    cursor: 'pointer',
                                    fontSize: '14px',
                                    fontWeight: readStatusTab === 'unread' ? 'bold' : 'normal'
                                }}
                                onClick={() => setReadStatusTab('unread')}
                            >
                                ❌ Chưa đọc ({readStatusData.stats.totalUnread})
                            </Box>
                        </Box>

                        {/* User List */}
                        <Box style={{
                            maxHeight: '300px',
                            overflowY: 'auto',
                            // Hide scrollbar on mobile
                            scrollbarWidth: 'none',
                            msOverflowStyle: 'none',
                            '&::-webkit-scrollbar': {
                                display: 'none'
                            }
                        }}>
                            {readStatusLoading ? (
                                <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                                    <LoadingIndicator />
                                </Box>
                            ) : (
                                <Box style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                    {readStatusTab === 'read' ? (
                                        readStatusData.readBy.length > 0 ? (
                                            readStatusData.readBy.map((item, index) => (
                                                <Box
                                                    key={index}
                                                    style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        padding: '10px',
                                                        backgroundColor: '#e8f5e8',
                                                        borderRadius: '6px',
                                                        borderLeft: '4px solid #4CAF50'
                                                    }}
                                                >
                                                    <Box style={{
                                                        width: '32px',
                                                        height: '32px',
                                                        borderRadius: '50%',
                                                        backgroundColor: '#4CAF50',
                                                        color: 'white',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        marginRight: '10px',
                                                        fontSize: '14px'
                                                    }}>
                                                        {item.user.role === 'student' ? '👨‍🎓' : '👨‍🏫'}
                                                    </Box>
                                                    <Box style={{ flex: 1 }}>
                                                        <Text style={{ fontWeight: 'bold', marginBottom: '2px' }}>
                                                            {item.user.name}
                                                        </Text>
                                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                                            {item.user.displayRole} • Đọc {item.timeAgo}
                                                        </Text>
                                                    </Box>
                                                    <Box style={{
                                                        padding: '2px 6px',
                                                        backgroundColor: '#4CAF50',
                                                        color: 'white',
                                                        borderRadius: '3px',
                                                        fontSize: '10px'
                                                    }}>
                                                        ✅ Đã đọc
                                                    </Box>
                                                </Box>
                                            ))
                                        ) : (
                                            <Text style={{ textAlign: 'center', color: '#666', padding: '20px' }}>
                                                Chưa có ai đọc thông báo này
                                            </Text>
                                        )
                                    ) : (
                                        readStatusData.unreadUsers.length > 0 ? (
                                            readStatusData.unreadUsers.map((user, index) => (
                                                <Box
                                                    key={index}
                                                    style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        padding: '10px',
                                                        backgroundColor: '#ffeaea',
                                                        borderRadius: '6px',
                                                        borderLeft: '4px solid #F44336'
                                                    }}
                                                >
                                                    <Box style={{
                                                        width: '32px',
                                                        height: '32px',
                                                        borderRadius: '50%',
                                                        backgroundColor: '#F44336',
                                                        color: 'white',
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        marginRight: '10px',
                                                        fontSize: '14px'
                                                    }}>
                                                        {user.studentId ? '👨‍🎓' : '👨‍🏫'}
                                                    </Box>
                                                    <Box style={{ flex: 1 }}>
                                                        <Text style={{ fontWeight: 'bold', marginBottom: '2px' }}>
                                                            {user.name}
                                                        </Text>
                                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                                            {user.displayRole}
                                                            {user.studentId && ` • ${user.studentId}`}
                                                        </Text>
                                                    </Box>
                                                    <Box style={{
                                                        padding: '2px 6px',
                                                        backgroundColor: '#F44336',
                                                        color: 'white',
                                                        borderRadius: '3px',
                                                        fontSize: '10px'
                                                    }}>
                                                        ❌ Chưa đọc
                                                    </Box>
                                                </Box>
                                            ))
                                        ) : (
                                            <Text style={{ textAlign: 'center', color: '#666', padding: '20px' }}>
                                                Tất cả đã đọc thông báo
                                            </Text>
                                        )
                                    )}
                                </Box>
                            )}
                        </Box>
                    </Box>
                )}
            </Modal>

            <Box
                style={{
                    position: 'fixed',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    zIndex: 100,
                    backgroundColor: '#fff',
                    borderTop: '1px solid #eee'
                }}
            >
                <BottomNavigation />
            </Box>
        </Box>
    );
};

export default AllAnnouncements;