import React, { useEffect, useState, useContext, useCallback } from 'react';
import { Box, Text, useNavigate, Select, Button } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import LoadingIndicator from '../components/LoadingIndicator';

const { Option } = Select;

const AllTeacherClasses = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [classes, setClasses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    // Date filter states
    const [filterType, setFilterType] = useState('week'); // 'day', 'week', 'month', 'year'
    const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
    const [selectedWeek, setSelectedWeek] = useState(() => {
        const today = new Date();
        const currentDay = today.getDay();
        const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay;
        const monday = new Date(today);
        monday.setDate(today.getDate() + mondayOffset);
        return monday.toISOString().split('T')[0];
    });
    const [selectedMonth, setSelectedMonth] = useState(() => {
        const today = new Date();
        return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}`;
    });
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
    const [session, setSession] = useState('all'); // 'all', 'morning', 'afternoon'

    // Helper functions for date calculations
    const getDateRange = useCallback(() => {
        let startDate, endDate;

        switch (filterType) {
            case 'day':
                startDate = selectedDate;
                endDate = selectedDate;
                break;
            case 'week':
                const weekStart = new Date(selectedWeek);
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekStart.getDate() + 6);
                startDate = weekStart.toISOString().split('T')[0];
                endDate = weekEnd.toISOString().split('T')[0];
                break;
            case 'month':
                const [year, month] = selectedMonth.split('-');
                const monthStart = new Date(parseInt(year), parseInt(month) - 1, 1);
                const monthEnd = new Date(parseInt(year), parseInt(month), 0);
                startDate = monthStart.toISOString().split('T')[0];
                endDate = monthEnd.toISOString().split('T')[0];
                break;
            case 'year':
                startDate = `${selectedYear}-01-01`;
                endDate = `${selectedYear}-12-31`;
                break;
            default:
                startDate = selectedDate;
                endDate = selectedDate;
        }

        return { startDate, endDate };
    }, [filterType, selectedDate, selectedWeek, selectedMonth, selectedYear]);

    const getFilterDisplayText = useCallback(() => {
        switch (filterType) {
            case 'day':
                return `Ngày ${new Date(selectedDate).toLocaleDateString('vi-VN')}`;
            case 'week':
                const weekStart = new Date(selectedWeek);
                const weekEnd = new Date(weekStart);
                weekEnd.setDate(weekStart.getDate() + 6);
                return `Tuần ${weekStart.getDate()}/${weekStart.getMonth() + 1} - ${weekEnd.getDate()}/${weekEnd.getMonth() + 1}`;
            case 'month':
                const [year, month] = selectedMonth.split('-');
                return `Tháng ${month}/${year}`;
            case 'year':
                return `Năm ${selectedYear}`;
            default:
                return '';
        }
    }, [filterType, selectedDate, selectedWeek, selectedMonth, selectedYear]);

    // Fetch all teacher classes with attendance statistics
    const fetchAllClasses = useCallback(async () => {
        if (!user) return;

        setLoading(true);
        setError('');
        try {
            const response = await authApi.get('/directory/teacher/classes');
            if (response.data.success) {
                const classesData = response.data.data || [];
                const { startDate, endDate } = getDateRange();

                // Fetch attendance statistics for each class
                const classesWithAttendance = await Promise.all(
                    classesData.map(async (classItem) => {
                        try {
                            // Build API URL with session filter
                            let apiUrl = `/attendance/statistics/class/${classItem.id}?startDate=${startDate}&endDate=${endDate}`;
                            if (session !== 'all') {
                                apiUrl += `&session=${session}`;
                            }

                            const attendanceResponse = await authApi.get(apiUrl);

                            if (attendanceResponse.data.success) {
                                const attendanceData = attendanceResponse.data.data;
                                const overview = attendanceData.overview;
                                const totalStudents = attendanceData.class.totalStudents || classItem.studentCount || 0;
                                const attendedStudents = overview.studentsAttended || 0;
                                const absentStudents = overview.studentsNotAttended || 0;
                                const attendanceRate = totalStudents > 0 ? Math.round((attendedStudents / totalStudents) * 100) : 0;

                                return {
                                    ...classItem,
                                    attendance: `${attendedStudents}/${totalStudents}`,
                                    progress: `${attendanceRate}%`,
                                    subjects: classItem.teachingSubjects?.join(', ') || '',
                                    periods: classItem.teachingPeriods?.length > 0 ?
                                        `Tiết ${classItem.teachingPeriods[0].periodNumber}` : 'Chưa có lịch',
                                    attendanceStats: {
                                        totalRecords: overview.totalAttendanceRecords || 0,
                                        present: attendedStudents,
                                        absent: absentStudents,
                                        late: overview.late || 0,
                                        rejected: overview.rejected || 0,
                                        rate: attendanceRate,
                                        sessions: attendanceData.sessions || []
                                    }
                                };
                            } else {
                                // Fallback if statistics API fails
                                return {
                                    ...classItem,
                                    attendance: '0/0',
                                    progress: '0%',
                                    subjects: classItem.teachingSubjects?.join(', ') || '',
                                    periods: 'Chưa có lịch',
                                    attendanceStats: {
                                        totalRecords: 0,
                                        present: 0,
                                        absent: 0,
                                        late: 0,
                                        rejected: 0,
                                        rate: 0,
                                        sessions: []
                                    }
                                };
                            }
                        } catch (err) {
                            console.error(`Error fetching attendance statistics for class ${classItem.id}:`, err);
                            return {
                                ...classItem,
                                attendance: '0/0',
                                progress: '0%',
                                subjects: classItem.teachingSubjects?.join(', ') || '',
                                periods: 'Chưa có lịch',
                                attendanceStats: {
                                    totalRecords: 0,
                                    present: 0,
                                    absent: 0,
                                    late: 0,
                                    rejected: 0,
                                    rate: 0,
                                    sessions: []
                                }
                            };
                        }
                    })
                );

                setClasses(classesWithAttendance);
            }
        } catch (err) {
            console.error('Error fetching teacher classes:', err);
            setError('Lỗi khi tải danh sách lớp học');
        } finally {
            setLoading(false);
        }
    }, [user, getDateRange, session]);

    // Fetch classes when dependencies change
    useEffect(() => {
        if (user && (user.role === 'teacher' || user.role === 'TEACHER')) {
            fetchAllClasses();
        }
    }, [user, fetchAllClasses]);

    // Check authentication
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && user.role !== 'teacher' && user.role !== 'TEACHER') {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    if (authLoading) {
        return (
            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
                <LoadingIndicator />
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Tất cả lớp học" 
                showBackButton={true} 
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />
            
            <Box style={{ padding: '15px' }}>
                {/* Filter Controls */}
                <Box style={{ backgroundColor: 'white', borderRadius: '8px', padding: '15px', marginBottom: '15px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                    <Text bold style={{ marginBottom: '10px' }}>Bộ lọc thời gian</Text>

                    {/* Filter Type Selection */}
                    <Box style={{ marginBottom: '10px' }}>
                        <Text style={{ fontSize: '14px', marginBottom: '5px' }}>Loại thời gian:</Text>
                        <Select
                            value={filterType}
                            onChange={(value) => setFilterType(value)}
                            style={{ width: '100%' }}
                        >
                            <Option value="day" title="Ngày" />
                            <Option value="week" title="Tuần" />
                            <Option value="month" title="Tháng" />
                            <Option value="year" title="Năm" />
                        </Select>
                    </Box>

                    {/* Date Selection based on filter type */}
                    {filterType === 'day' && (
                        <Box style={{ marginBottom: '10px' }}>
                            <Text style={{ fontSize: '14px', marginBottom: '5px' }}>Chọn ngày:</Text>
                            <input
                                type="date"
                                value={selectedDate}
                                onChange={(e) => setSelectedDate(e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '8px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px',
                                    fontSize: '14px'
                                }}
                            />
                        </Box>
                    )}

                    {filterType === 'week' && (
                        <Box style={{ marginBottom: '10px' }}>
                            <Text style={{ fontSize: '14px', marginBottom: '5px' }}>Chọn tuần (Thứ 2):</Text>
                            <input
                                type="date"
                                value={selectedWeek}
                                onChange={(e) => setSelectedWeek(e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '8px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px',
                                    fontSize: '14px'
                                }}
                            />
                        </Box>
                    )}

                    {filterType === 'month' && (
                        <Box style={{ marginBottom: '10px' }}>
                            <Text style={{ fontSize: '14px', marginBottom: '5px' }}>Chọn tháng:</Text>
                            <input
                                type="month"
                                value={selectedMonth}
                                onChange={(e) => setSelectedMonth(e.target.value)}
                                style={{
                                    width: '100%',
                                    padding: '8px',
                                    border: '1px solid #ddd',
                                    borderRadius: '4px',
                                    fontSize: '14px'
                                }}
                            />
                        </Box>
                    )}

                    {filterType === 'year' && (
                        <Box style={{ marginBottom: '10px' }}>
                            <Text style={{ fontSize: '14px', marginBottom: '5px' }}>Chọn năm:</Text>
                            <Select
                                value={selectedYear}
                                onChange={(value) => setSelectedYear(value)}
                                style={{ width: '100%' }}
                            >
                                {Array.from({ length: 10 }, (_, i) => {
                                    const year = new Date().getFullYear() - 5 + i;
                                    return <Option key={year} value={year.toString()} title={year.toString()} />;
                                })}
                            </Select>
                        </Box>
                    )}

                    {/* Session Selection */}
                    <Box style={{ marginBottom: '10px' }}>
                        <Text style={{ fontSize: '14px', marginBottom: '5px' }}>Buổi học:</Text>
                        <Select
                            value={session}
                            onChange={(value) => setSession(value)}
                            style={{ width: '100%' }}
                        >
                            <Option value="all" title="Tất cả" />
                            <Option value="morning" title="Buổi sáng" />
                            <Option value="afternoon" title="Buổi chiều" />
                        </Select>
                    </Box>

                    {/* Apply Button */}
                    <Button
                        onClick={fetchAllClasses}
                        style={{
                            width: '100%',
                            backgroundColor: '#0068ff',
                            color: 'white',
                            border: 'none',
                            borderRadius: '6px',
                            padding: '10px',
                            fontSize: '14px'
                        }}
                    >
                        Áp dụng bộ lọc
                    </Button>
                </Box>

                {loading ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : error ? (
                    <Box style={{ textAlign: 'center', padding: '20px' }}>
                        <Text style={{ color: 'red' }}>{error}</Text>
                    </Box>
                ) : (
                    <>
                        {/* Summary Statistics */}
                        <Box style={{ backgroundColor: 'white', borderRadius: '8px', padding: '15px', marginBottom: '15px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                            <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                                <Text bold size="large">Tổng quan</Text>
                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                    {getFilterDisplayText()}
                                </Text>
                            </Box>

                            {(() => {
                                const totals = classes.reduce((acc, cls) => {
                                    const stats = cls.attendanceStats || {};
                                    acc.totalStudents += cls.studentCount || 0;
                                    acc.present += stats.present || 0;
                                    acc.absent += stats.absent || 0;
                                    acc.late += stats.late || 0;
                                    acc.totalRecords += stats.totalRecords || 0;
                                    return acc;
                                }, { totalStudents: 0, present: 0, absent: 0, late: 0, totalRecords: 0 });

                                const overallRate = totals.totalStudents > 0 ?
                                    Math.round((totals.present / totals.totalStudents) * 100) : 0;

                                return (
                                    <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '10px' }}>
                                        <Box style={{ textAlign: 'center', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
                                            <Text style={{ fontSize: '20px', fontWeight: 'bold', color: '#0068ff' }}>
                                                {classes.length}
                                            </Text>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>Lớp học</Text>
                                        </Box>
                                        <Box style={{ textAlign: 'center', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '6px' }}>
                                            <Text style={{ fontSize: '20px', fontWeight: 'bold', color: '#0068ff' }}>
                                                {totals.totalStudents}
                                            </Text>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>Học sinh</Text>
                                        </Box>
                                        <Box style={{ textAlign: 'center', padding: '10px', backgroundColor: '#e8f5e8', borderRadius: '6px' }}>
                                            <Text style={{ fontSize: '20px', fontWeight: 'bold', color: '#4CAF50' }}>
                                                {totals.present}
                                            </Text>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>Có mặt</Text>
                                        </Box>
                                        <Box style={{ textAlign: 'center', padding: '10px', backgroundColor: '#ffeaea', borderRadius: '6px' }}>
                                            <Text style={{ fontSize: '20px', fontWeight: 'bold', color: '#F44336' }}>
                                                {totals.absent}
                                            </Text>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>Vắng</Text>
                                        </Box>
                                    </Box>
                                );
                            })()}

                            <Box style={{ marginTop: '10px', textAlign: 'center' }}>
                                <Text style={{ fontSize: '14px', color: '#666' }}>
                                    Tỷ lệ điểm danh trung bình:
                                    <Text bold style={{
                                        color: (() => {
                                            const totals = classes.reduce((acc, cls) => {
                                                const stats = cls.attendanceStats || {};
                                                acc.totalStudents += cls.studentCount || 0;
                                                acc.present += stats.present || 0;
                                                return acc;
                                            }, { totalStudents: 0, present: 0 });
                                            const rate = totals.totalStudents > 0 ? Math.round((totals.present / totals.totalStudents) * 100) : 0;
                                            return rate >= 80 ? '#4CAF50' : rate >= 60 ? '#FF9800' : '#F44336';
                                        })()
                                    }}>
                                        {(() => {
                                            const totals = classes.reduce((acc, cls) => {
                                                const stats = cls.attendanceStats || {};
                                                acc.totalStudents += cls.studentCount || 0;
                                                acc.present += stats.present || 0;
                                                return acc;
                                            }, { totalStudents: 0, present: 0 });
                                            return totals.totalStudents > 0 ? Math.round((totals.present / totals.totalStudents) * 100) : 0;
                                        })()}%
                                    </Text>
                                </Text>
                            </Box>
                        </Box>

                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                            <Text bold size="large">
                                Danh sách lớp học ({classes.length})
                            </Text>
                            <Box
                                style={{
                                    cursor: 'pointer',
                                    padding: '8px',
                                    borderRadius: '50%',
                                    backgroundColor: '#f0f0f0',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                }}
                                onClick={fetchAllClasses}
                            >
                                <Text style={{ fontSize: '16px' }}>🔄</Text>
                            </Box>
                        </Box>
                        
                        <Box className="classes-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(1, 1fr)', gap: '15px' }}>
                            {classes.map((classItem, index) => (
                                <Box
                                    key={index}
                                    className="class-card"
                                    style={{
                                        backgroundColor: 'white',
                                        borderRadius: '10px',
                                        padding: '15px',
                                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                        cursor: 'pointer'
                                    }}
                                    onClick={() => navigate(`/teacher-class-detail/${classItem.id}`)}
                                >
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '12px' }}>
                                        <Box style={{ flex: 1 }}>
                                            <Text className="class-name" bold size="large" style={{ marginBottom: '5px' }}>
                                                {classItem.name}
                                            </Text>
                                            <Text style={{ fontSize: '14px', color: '#666', marginBottom: '5px' }}>
                                                {classItem.subjects}
                                            </Text>
                                            <Text style={{ fontSize: '12px', color: '#888' }}>
                                                {classItem.classRoom} • {classItem.periods}
                                            </Text>
                                        </Box>
                                        <Box style={{ textAlign: 'right' }}>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                                {classItem.studentCount} học sinh
                                            </Text>
                                        </Box>
                                    </Box>

                                    {/* Attendance Statistics */}
                                    <Box style={{ backgroundColor: '#f8f9fa', borderRadius: '6px', padding: '10px', marginBottom: '10px' }}>
                                        <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                                            <Text style={{ fontSize: '14px', fontWeight: 'bold' }}>
                                                Điểm danh: {classItem.attendance}
                                            </Text>
                                            <Text style={{
                                                fontSize: '14px',
                                                fontWeight: 'bold',
                                                color: classItem.attendanceStats?.rate >= 80 ? '#4CAF50' :
                                                       classItem.attendanceStats?.rate >= 60 ? '#FF9800' : '#F44336'
                                            }}>
                                                {classItem.progress}
                                            </Text>
                                        </Box>

                                        {classItem.attendanceStats && (
                                            <Box style={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '8px', marginBottom: '8px' }}>
                                                <Box style={{ textAlign: 'center' }}>
                                                    <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#4CAF50' }}>
                                                        {classItem.attendanceStats.present}
                                                    </Text>
                                                    <Text style={{ fontSize: '11px', color: '#666' }}>Có mặt</Text>
                                                </Box>
                                                <Box style={{ textAlign: 'center' }}>
                                                    <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#F44336' }}>
                                                        {classItem.attendanceStats.absent}
                                                    </Text>
                                                    <Text style={{ fontSize: '11px', color: '#666' }}>Vắng</Text>
                                                </Box>
                                                <Box style={{ textAlign: 'center' }}>
                                                    <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#FF9800' }}>
                                                        {classItem.attendanceStats.late}
                                                    </Text>
                                                    <Text style={{ fontSize: '11px', color: '#666' }}>Muộn</Text>
                                                </Box>
                                                <Box style={{ textAlign: 'center' }}>
                                                    <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#666' }}>
                                                        {classItem.attendanceStats.totalRecords}
                                                    </Text>
                                                    <Text style={{ fontSize: '11px', color: '#666' }}>Buổi học</Text>
                                                </Box>
                                            </Box>
                                        )}

                                        <Box className="attendance-bar" style={{ height: '8px', backgroundColor: '#e0e0e0', borderRadius: '4px', position: 'relative' }}>
                                            <Box
                                                className="attendance-progress"
                                                style={{
                                                    width: classItem.progress,
                                                    height: '100%',
                                                    backgroundColor: classItem.attendanceStats?.rate >= 80 ? '#4CAF50' :
                                                                   classItem.attendanceStats?.rate >= 60 ? '#FF9800' : '#F44336',
                                                    borderRadius: '4px',
                                                    transition: 'width 0.3s ease'
                                                }}
                                            />
                                        </Box>
                                    </Box>

                                    {/* Sessions Summary */}
                                    {classItem.attendanceStats?.sessions && classItem.attendanceStats.sessions.length > 0 && (
                                        <Box style={{ marginTop: '10px' }}>
                                            <Text style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>
                                                Chi tiết buổi học:
                                            </Text>
                                            <Box style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                                                {classItem.attendanceStats.sessions.slice(0, 5).map((sessionData, idx) => (
                                                    <Box
                                                        key={idx}
                                                        style={{
                                                            backgroundColor: sessionData.present > sessionData.absent ? '#e8f5e8' : '#ffeaea',
                                                            color: sessionData.present > sessionData.absent ? '#2e7d32' : '#c62828',
                                                            padding: '2px 6px',
                                                            borderRadius: '3px',
                                                            fontSize: '10px'
                                                        }}
                                                    >
                                                        {new Date(sessionData.date).getDate()}/{new Date(sessionData.date).getMonth() + 1} {sessionData.sessionName}
                                                    </Box>
                                                ))}
                                                {classItem.attendanceStats.sessions.length > 5 && (
                                                    <Text style={{ fontSize: '10px', color: '#666' }}>
                                                        +{classItem.attendanceStats.sessions.length - 5} buổi khác
                                                    </Text>
                                                )}
                                            </Box>
                                        </Box>
                                    )}
                                </Box>
                            ))}
                        </Box>
                        
                        {classes.length === 0 && (
                            <Box style={{ textAlign: 'center', padding: '40px 20px' }}>
                                <Text style={{ color: '#666' }}>Chưa có lớp học nào được phân công</Text>
                            </Box>
                        )}
                    </>
                )}
            </Box>
            
            <BottomNavigationEdu />
        </Box>
    );
};

export default AllTeacherClasses;
