import React, { useEffect, useState, useContext } from 'react';
import { Box, Text, useNavigate } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import LoadingIndicator from '../components/LoadingIndicator';

const AllTeacherClasses = () => {
    const navigate = useNavigate();
    const { user, loading: authLoading } = useContext(AuthContext);
    const [classes, setClasses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    // Fetch all teacher classes
    useEffect(() => {
        const fetchAllClasses = async () => {
            if (!user) return;
            
            setLoading(true);
            try {
                const response = await authApi.get('/directory/teacher/classes');
                if (response.data.success) {
                    const classesData = response.data.data || [];
                    
                    // Fetch attendance data for each class
                    const classesWithAttendance = await Promise.all(
                        classesData.map(async (classItem) => {
                            try {
                                const today = new Date().toISOString().split('T')[0];
                                const session = new Date().getHours() < 12 ? 'morning' : 'afternoon';
                                const attendanceResponse = await authApi.get(
                                    `/attendance/class/${classItem.id}?date=${today}&session=${session}`
                                );
                                
                                const attendanceData = attendanceResponse.data.success ? attendanceResponse.data.data : [];
                                const presentCount = attendanceData.filter(record => record.status === 'present').length;
                                const totalStudents = classItem.studentCount || 0;
                                const attendanceRate = totalStudents > 0 ? Math.round((presentCount / totalStudents) * 100) : 0;
                                
                                return {
                                    ...classItem,
                                    attendance: `${presentCount}/${totalStudents}`,
                                    progress: `${attendanceRate}%`,
                                    subjects: classItem.teachingSubjects?.join(', ') || '',
                                    periods: classItem.teachingPeriods?.length > 0 ? 
                                        `Tiết ${classItem.teachingPeriods[0].periodNumber}` : 'Chưa có lịch'
                                };
                            } catch (err) {
                                console.error(`Error fetching attendance for class ${classItem.id}:`, err);
                                return {
                                    ...classItem,
                                    attendance: '0/0',
                                    progress: '0%',
                                    subjects: classItem.teachingSubjects?.join(', ') || '',
                                    periods: 'Chưa có lịch'
                                };
                            }
                        })
                    );
                    
                    setClasses(classesWithAttendance);
                }
            } catch (err) {
                console.error('Error fetching teacher classes:', err);
                setError('Lỗi khi tải danh sách lớp học');
            } finally {
                setLoading(false);
            }
        };

        if (user && (user.role === 'teacher' || user.role === 'TEACHER')) {
            fetchAllClasses();
        }
    }, [user]);

    // Check authentication
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && user.role !== 'teacher' && user.role !== 'TEACHER') {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    if (authLoading) {
        return (
            <Box style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '100vh' }}>
                <LoadingIndicator />
            </Box>
        );
    }

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu 
                title="Tất cả lớp học" 
                showBackButton={true} 
                onBackClick={() => navigate(-1)}
            />
            <HeaderSpacer />
            
            <Box style={{ padding: '15px' }}>
                {loading ? (
                    <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                        <LoadingIndicator />
                    </Box>
                ) : error ? (
                    <Box style={{ textAlign: 'center', padding: '20px' }}>
                        <Text style={{ color: 'red' }}>{error}</Text>
                    </Box>
                ) : (
                    <>
                        <Text bold size="large" style={{ marginBottom: '15px' }}>
                            Danh sách lớp học ({classes.length})
                        </Text>
                        
                        <Box className="classes-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(1, 1fr)', gap: '15px' }}>
                            {classes.map((classItem, index) => (
                                <Box 
                                    key={index} 
                                    className="class-card" 
                                    style={{ 
                                        backgroundColor: 'white', 
                                        borderRadius: '10px', 
                                        padding: '15px', 
                                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                        cursor: 'pointer'
                                    }}
                                    onClick={() => navigate(`/teacher-class-detail/${classItem.id}`)}
                                >
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '10px' }}>
                                        <Box style={{ flex: 1 }}>
                                            <Text className="class-name" bold size="large" style={{ marginBottom: '5px' }}>
                                                {classItem.name}
                                            </Text>
                                            <Text style={{ fontSize: '14px', color: '#666', marginBottom: '5px' }}>
                                                {classItem.subjects}
                                            </Text>
                                            <Text style={{ fontSize: '12px', color: '#888' }}>
                                                {classItem.classRoom} • {classItem.periods}
                                            </Text>
                                        </Box>
                                        <Box style={{ textAlign: 'right' }}>
                                            <Text style={{ fontSize: '12px', color: '#666' }}>
                                                {classItem.studentCount} học sinh
                                            </Text>
                                        </Box>
                                    </Box>
                                    
                                    <Box style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '10px' }}>
                                        <Text style={{ fontSize: '14px' }}>
                                            Điểm danh: <Text bold>{classItem.attendance}</Text>
                                        </Text>
                                        <Text style={{ fontSize: '14px', color: '#0068ff', fontWeight: 'bold' }}>
                                            {classItem.progress}
                                        </Text>
                                    </Box>
                                    
                                    <Box className="attendance-bar" style={{ height: '6px', backgroundColor: '#e0e0e0', borderRadius: '3px', marginTop: '8px', position: 'relative' }}>
                                        <Box 
                                            className="attendance-progress" 
                                            style={{ 
                                                width: classItem.progress, 
                                                height: '100%', 
                                                backgroundColor: '#0068ff', 
                                                borderRadius: '3px' 
                                            }} 
                                        />
                                    </Box>
                                </Box>
                            ))}
                        </Box>
                        
                        {classes.length === 0 && (
                            <Box style={{ textAlign: 'center', padding: '40px 20px' }}>
                                <Text style={{ color: '#666' }}>Chưa có lớp học nào được phân công</Text>
                            </Box>
                        )}
                    </>
                )}
            </Box>
            
            <BottomNavigationEdu />
        </Box>
    );
};

export default AllTeacherClasses;
