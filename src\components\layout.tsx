import { getSystemInfo } from 'zmp-sdk';
import { AnimationRoutes, App, Route, SnackbarProvider, ZMPRouter } from 'zmp-ui';
import { AppProps } from 'zmp-ui/app';
import { useEffect, useState, useRef, useMemo } from 'react';
import { useNavigate, useLocation } from 'zmp-ui';
import useSwipeNavigation from '@/hooks/useSwipeNavigation';
import Login from '@/pages/Login';
import StudentEdu from '@/pages/StudentEdu';
import TeacherEdu from '@/pages/TeacherEdu';
import Profile from '@/pages/Profile';
import ScheduleEdu from '@/pages/ScheduleEdu';
import AllAnnouncements from '@/pages/AllAnnouncements';
import AllEvents from '@/pages/AllEvents';
import AllNews from '@/pages/AllNews';
import NewsDetail from '@/pages/NewsDetail';
import { parseJwt } from '@/utils/jwt';
import { SchoolYearProvider } from '@/context/SchoolYearContext';
import { AuthProvider } from '@/context/AuthContext';
import AuthInitializer from '@/components/AuthInitializer';
import Grades from '@/pages/Grades';
import Exams from '@/pages/Exams';
import ExerciseDetail from '@/pages/ExerciseDetail';
import Attempt from '@/pages/Attempt';
import AttemptResults from '@/pages/AttemptResults';
import ExerciseListBySubject from '@/pages/ExerciseListBySubject';
import Attendance from '@/pages/Attendance';
import Directory from '@/pages/Directory';
import AllTeacherClasses from '@/pages/AllTeacherClasses';
import TeacherSchedule from '@/pages/TeacherSchedule';

// Route mapping để biết component nào render cho path nào
const getComponentForPath = (path: string, navigate: any) => {
  // Helper function to wrap components with AuthInitializer
  const withAuth = (Component: any) => (
    <AuthInitializer>
      <Component />
    </AuthInitializer>
  );

  if (path === '/login') return <Login />;
  if (path === '/student') return withAuth(StudentEdu);
  if (path === '/teacher') return withAuth(TeacherEdu);
  if (path === '/profile') return withAuth(Profile);
  if (path === '/schedule') return withAuth(ScheduleEdu);
  if (path === '/announcements') return withAuth(AllAnnouncements);
  if (path === '/all-events') return withAuth(AllEvents);
  if (path === '/grades') return withAuth(Grades);
  if (path === '/exams') return withAuth(Exams);
  if (path === '/attendance') return withAuth(Attendance);
  if (path === '/directories') return withAuth(Directory);
  if (path === '/news') return withAuth(AllNews);
  if (path === '/teacher-classes') return withAuth(AllTeacherClasses);
  if (path === '/teacher-schedule') return withAuth(TeacherSchedule);
  
  // Dynamic routes
  if (path.startsWith('/exercises/')) return withAuth(ExerciseDetail);
  if (path.startsWith('/attempt/') && path.endsWith('/results')) return withAuth(AttemptResults);
  if (path.startsWith('/attempt/')) return withAuth(Attempt);
  if (path.startsWith('/subjects/') && path.endsWith('/exercises')) return withAuth(ExerciseListBySubject);
  if (path.startsWith('/news/')) return withAuth(NewsDetail);
  
  // Fallback
  return withAuth(StudentEdu);
};

const AuthRedirect = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      const parsedToken = parseJwt(token);
      if (parsedToken && parsedToken.exp * 1000 > Date.now()) {
        if (parsedToken.user.role === 'student') {
          navigate('/student', { replace: true });
        } else if (parsedToken.user.role === 'teacher' || parsedToken.user.role === 'TEACHER') {
          navigate('/teacher', { replace: true });
        } else {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          navigate('/login', { replace: true });
        }
      } else {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        navigate('/login', { replace: true });
      }
    } else {
      navigate('/login', { replace: true });
    }
  }, [navigate]);

  return null;
};

const SwipeWrapper = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Track navigation history
  const historyStackRef = useRef<string[]>([]);
  const [previousPath, setPreviousPath] = useState<string>('');
  
  // Update history when location changes
  useEffect(() => {
    const currentPath = location.pathname;
    const lastPath = historyStackRef.current[historyStackRef.current.length - 1];
    
    if (currentPath !== lastPath) {
      // Set previous path before updating history
      if (historyStackRef.current.length > 0) {
        setPreviousPath(historyStackRef.current[historyStackRef.current.length - 1]);
      }
      
      // Add to history
      historyStackRef.current.push(currentPath);
      
      // Keep only last 10 entries to prevent memory issues
      if (historyStackRef.current.length > 10) {
        historyStackRef.current = historyStackRef.current.slice(-10);
      }
    }
  }, [location.pathname]);
  
  // Sử dụng hook swipe navigation mới với previous screen preview
  const { 
    isActiveSwipe, 
    swipeProgress, 
    getCurrentScreenStyles, 
    getPreviousScreenStyles,
    previousScreenVisible,
    canShowPreviousScreen
  } = useSwipeNavigation({
    threshold: 100, // Khoảng cách để trigger back navigation
    velocityThreshold: 0.4, // Vận tốc tối thiểu
    edgeThreshold: 30, // Vùng edge để bắt đầu swipe
    maxEdgeStart: 20, // Chỉ cho phép swipe từ 20px đầu màn hình
    resistanceDistance: 50,
    enablePreviousScreenPreview: true, // Enable Facebook-like effect
    
    onSwipeComplete: () => {
      // Navigate back khi swipe hoàn thành
      try {
        // Kiểm tra nếu có thể quay lại
        if (window.history.length > 1) {
          navigate(-1);
        } else {
          // Fallback về trang chính nếu không có history
          const token = localStorage.getItem('token');
          if (token) {
            const parsedToken = parseJwt(token);
            if (parsedToken?.user?.role === 'student') {
              navigate('/student', { replace: true });
            } else if (parsedToken?.user?.role === 'teacher' || parsedToken?.user?.role === 'TEACHER') {
              navigate('/teacher', { replace: true });
            }
          }
        }
      } catch (error) {
        console.error('Navigation error:', error);
      }
    },
    
    enableHapticFeedback: true,
    enableVisualFeedback: false, // Tắt visual feedback mặc định, tự custom
    debounceMs: 100
  });

  // Back indicator style (only show if no previous screen preview)
  const backIndicatorStyle: any = {
    position: 'fixed',
    left: `${isActiveSwipe && !canShowPreviousScreen ? Math.min(swipeProgress * 40, 25) : -40}px`,
    top: '50%',
    transform: 'translateY(-50%)',
    width: '32px',
    height: '32px',
    backgroundColor: '#0068ff',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    fontSize: '16px',
    fontWeight: 'bold',
    opacity: isActiveSwipe && !canShowPreviousScreen ? Math.min(swipeProgress * 1.5, 1) : 0,
    transition: isActiveSwipe ? 'none' : 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    zIndex: 9998,
    boxShadow: '0 4px 12px rgba(0, 104, 255, 0.4)',
    pointerEvents: 'none'
  };

  // Overlay effect - darken the background when swiping
  const overlayStyle: any = {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: `rgba(0, 0, 0, ${isActiveSwipe ? Math.min(swipeProgress * 0.3, 0.3) : 0})`,
    pointerEvents: 'none',
    transition: isActiveSwipe ? 'none' : 'background 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    zIndex: canShowPreviousScreen ? 0 : 9997 // Lower z-index when showing previous screen
  };

  // Memoize previous screen component để tránh re-render không cần thiết
  const previousScreenComponent = useMemo(() => {
    if (!previousPath) return null;
    return getComponentForPath(previousPath, navigate);
  }, [previousPath, navigate]);

  return (
    <>
      {/* Previous Screen Preview */}
      {canShowPreviousScreen && previousScreenVisible && previousScreenComponent && (
        <div style={getPreviousScreenStyles()}>
          {previousScreenComponent}
        </div>
      )}
      
      {/* Overlay Effect */}
      <div style={overlayStyle} />
      
      {/* Back Indicator (only when not showing previous screen) */}
      <div style={backIndicatorStyle}>
        ←
      </div>
      
      {/* Main Content */}
      <div style={getCurrentScreenStyles()}>
        {children}
      </div>
    </>
  );
};

const Layout = () => {
  return (
    <App theme={getSystemInfo().zaloTheme as AppProps['theme']}>
      <SchoolYearProvider>
        <SnackbarProvider>
          <AuthProvider>
            <ZMPRouter>
              <SwipeWrapper>
                <AnimationRoutes>
                  <Route path="/" element={<AuthRedirect />} />
                  <Route path="/login" element={<Login />} />
                  <Route
                    path="/student"
                    element={
                      <AuthInitializer>
                        <StudentEdu />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/teacher"
                    element={
                      <AuthInitializer>
                        <TeacherEdu />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/profile"
                    element={
                      <AuthInitializer>
                        <Profile />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/schedule"
                    element={
                      <AuthInitializer>
                        <ScheduleEdu />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/announcements"
                    element={
                      <AuthInitializer>
                        <AllAnnouncements />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/all-events"
                    element={
                      <AuthInitializer>
                        <AllEvents />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/grades"
                    element={
                      <AuthInitializer>
                        <Grades />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/exams"
                    element={
                      <AuthInitializer>
                        <Exams />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/exercises/:examId"
                    element={
                      <AuthInitializer>
                        <ExerciseDetail />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/attempt/:attemptId"
                    element={
                      <AuthInitializer>
                        <Attempt />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/attempt/:attemptId/results"
                    element={
                      <AuthInitializer>
                        <AttemptResults />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/subjects/:subjectId/exercises"
                    element={
                      <AuthInitializer>
                        <ExerciseListBySubject />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/attendance"
                    element={
                      <AuthInitializer>
                        <Attendance />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/directories"
                    element={
                      <AuthInitializer>
                        <Directory />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/news"
                    element={
                      <AuthInitializer>
                        <AllNews />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/news/:id"
                    element={
                      <AuthInitializer>
                        <NewsDetail />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/teacher-classes"
                    element={
                      <AuthInitializer>
                        <AllTeacherClasses />
                      </AuthInitializer>
                    }
                  />
                  <Route
                    path="/teacher-schedule"
                    element={
                      <AuthInitializer>
                        <TeacherSchedule />
                      </AuthInitializer>
                    }
                  />
                </AnimationRoutes>
              </SwipeWrapper>
            </ZMPRouter>
          </AuthProvider>
        </SnackbarProvider>
      </SchoolYearProvider>
    </App>
  );
};

export default Layout;