import React, { useEffect, useState, useRef, useContext, useCallback } from 'react';
import { Box, Text, List, useNavigate, Modal } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { authApi } from '../utils/api';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import { formatEventDate, formatEventTime } from '../utils/dateUtils';
import Loading from '../components/Loading';
import NewsComponent from '../components/NewsComponent';
import LoadingIndicator from '../components/LoadingIndicator';

const StudentEdu = () => {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [eventsLoading, setEventsLoading] = useState(false);
    const { user, classId, loading: authLoading } = useContext(AuthContext);
    const [announcements, setAnnouncements] = useState([]);
    const [events, setEvents] = useState([]);
    const [eventPage, setEventPage] = useState(1);
    const [totalEventPages, setTotalEventPages] = useState(1);
    const observerRef = useRef(null);
    const eventsContainerRef = useRef(null);
    const hasLoadedInitialEvents = useRef(false);
    const isLoadingMore = useRef(false);
    const loadedEventIds = useRef(new Set()); // Theo dõi các event đã tải để tránh trùng lặp

    // Recent announcements states
    const [recentAnnouncements, setRecentAnnouncements] = useState([]);
    const [announcementsLoading, setAnnouncementsLoading] = useState(true);
    const [unreadCount, setUnreadCount] = useState(0);
    const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
    const [announcementDetailVisible, setAnnouncementDetailVisible] = useState(false);
    // Kiểm tra xác thực
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && user.role !== 'student') {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Fetch recent announcements
    const fetchRecentAnnouncements = useCallback(async () => {
        if (!user) return;
        setAnnouncementsLoading(true);
        try {
            const response = await authApi.get('/announcements/recent?limit=5');
            if (Array.isArray(response.data)) {
                setRecentAnnouncements(response.data);
            }
        } catch (err) {
            console.error('Error fetching recent announcements:', err);
        } finally {
            setAnnouncementsLoading(false);
        }
    }, [user]);

    // Fetch unread count
    const fetchUnreadCount = useCallback(async () => {
        if (!user) return;
        try {
            const response = await authApi.get('/announcements/unread?limit=10');
            if (response.data.success) {
                setUnreadCount(response.data.data.unreadCount || 0);
            }
        } catch (err) {
            console.error('Error fetching unread count:', err);
        }
    }, [user]);

    // Mark announcement as read
    const markAsRead = useCallback(async (announcementId) => {
        try {
            await authApi.post(`/announcements/${announcementId}/read`);
            // Update local state
            setRecentAnnouncements(prev =>
                prev.map(ann =>
                    ann.id === announcementId ? { ...ann, isRead: true } : ann
                )
            );
            // Update unread count
            setUnreadCount(prev => Math.max(0, prev - 1));
        } catch (err) {
            console.error('Error marking announcement as read:', err);
        }
    }, []);

    // Fetch announcement detail
    const fetchAnnouncementDetail = useCallback(async (announcementId) => {
        try {
            const response = await authApi.get(`/announcements/${announcementId}`);
            setSelectedAnnouncement(response.data);
            setAnnouncementDetailVisible(true);
            // Mark as read when viewing detail
            if (!response.data.isRead) {
                markAsRead(announcementId);
            }
        } catch (err) {
            console.error('Error fetching announcement detail:', err);
        }
    }, [markAsRead]);

    // Lấy thông báo mới nhất (legacy - keep for backward compatibility)
    useEffect(() => {
        if (classId) {
            setLoading(true);
            authApi
                .get(`/announcements/recent?classId=${classId}&limit=2`)
                .then((response) => {
                    setAnnouncements(response.data);
                    setLoading(false);
                })
                .catch((err) => {
                    console.error('Error fetching announcements:', err);
                    setLoading(false);
                });
        }
    }, [classId]);

    // Load recent announcements and unread count
    useEffect(() => {
        if (user) {
            fetchRecentAnnouncements();
            fetchUnreadCount();
        }
    }, [user, fetchRecentAnnouncements, fetchUnreadCount]);

    // Lấy sự kiện sắp tới với phân trang
    const fetchEvents = useCallback(async (page, isInitialLoad = false) => {
        if (!classId || isLoadingMore.current) return;

        isLoadingMore.current = true;
        setEventsLoading(true);

        try {
            const response = await authApi.get(`/events/upcoming?classId=${classId}&page=${page}&limit=3`);

            // Xử lý phản hồi API
            let eventsData = [];
            let totalPages = 1;

            // API trả về định dạng { events: [], totalPages: number }
            if (response.data && Array.isArray(response.data.events)) {
                eventsData = response.data.events;
                totalPages = response.data.totalPages || 1;
            } else if (Array.isArray(response.data)) {
                // Hỗ trợ cả trường hợp API trả về mảng events trực tiếp
                eventsData = response.data;
                totalPages = 2; // Giả định có ít nhất 2 trang
            }

            // Kiểm tra và lọc ra các sự kiện trùng lặp
            const uniqueEvents = [];
            for (const event of eventsData) {
                const eventId = event._id;
                if (!loadedEventIds.current.has(eventId)) {
                    loadedEventIds.current.add(eventId);
                    uniqueEvents.push(event);
                }
            }

            // Cập nhật state
            if (page === 1 || isInitialLoad) {
                // Reset lại dữ liệu khi tải trang đầu tiên
                loadedEventIds.current = new Set(uniqueEvents.map(e => e._id));
                setEvents(uniqueEvents);
            } else {
                setEvents(prev => [...prev, ...uniqueEvents]);
            }

            setTotalEventPages(totalPages);

            if (isInitialLoad) {
                hasLoadedInitialEvents.current = true;
            }
        } catch (err) {
            console.error('Error fetching events:', err);
            if (isInitialLoad) {
                hasLoadedInitialEvents.current = true;
            }
        } finally {
            setEventsLoading(false);
            isLoadingMore.current = false;
        }
    }, [classId]);

    // Tải dữ liệu ban đầu
    useEffect(() => {
        if (classId && !hasLoadedInitialEvents.current) {
            fetchEvents(1, true);
        }
    }, [classId, fetchEvents]);

    // Xử lý khi eventPage thay đổi
    useEffect(() => {
        if (hasLoadedInitialEvents.current && eventPage > 1 && !isLoadingMore.current) {
            fetchEvents(eventPage, false);
        }
    }, [eventPage, fetchEvents]);

    // Load thêm sự kiện khi click nút "Xem thêm"
    const handleLoadMore = () => {
        if (eventPage < totalEventPages && !eventsLoading && !isLoadingMore.current) {
            setEventPage(prev => prev + 1);
        }
    };

    // Thiết lập IntersectionObserver
    useEffect(() => {
        if (!observerRef.current || isLoadingMore.current || eventPage >= totalEventPages) return;

        const observer = new IntersectionObserver(
            (entries) => {
                const entry = entries[0];

                if (entry.isIntersecting && !isLoadingMore.current && eventPage < totalEventPages) {
                    setEventPage(prev => prev + 1);
                }
            },
            { threshold: 0.5 }
        );

        const currentRef = observerRef.current;
        if (currentRef) {
            observer.observe(currentRef);
        }

        return () => {
            if (currentRef) {
                observer.unobserve(currentRef);
            }
            observer.disconnect();
        };
    }, [events.length, eventPage, totalEventPages]);

    // Xử lý scroll ngang với throttle
    const handleScroll = useCallback(() => {
        if (isLoadingMore.current || eventPage >= totalEventPages) return;

        const container = eventsContainerRef.current;
        if (!container) return;

        const scrollPosition = container.scrollLeft;
        const containerWidth = container.clientWidth;
        const scrollWidth = container.scrollWidth;

        const isNearEnd = scrollWidth - (scrollPosition + containerWidth) < 100;

        if (isNearEnd && !isLoadingMore.current && eventPage < totalEventPages) {
            setEventPage(prev => prev + 1);
        }
    }, [eventPage, totalEventPages]);

    // Gắn sự kiện scroll với throttle
    useEffect(() => {
        const container = eventsContainerRef.current;
        if (!container || eventPage >= totalEventPages) return;

        let isThrottled = false;
        const throttledScroll = () => {
            if (!isThrottled) {
                handleScroll();
                isThrottled = true;
                setTimeout(() => {
                    isThrottled = false;
                }, 300);
            }
        };

        container.addEventListener('scroll', throttledScroll);
        return () => container.removeEventListener('scroll', throttledScroll);
    }, [handleScroll, eventPage, totalEventPages]);

    // Trạng thái hiển thị
    const showEventsLoading = eventsLoading && events.length === 0;
    const showNoEvents = !eventsLoading && events.length === 0 && hasLoadedInitialEvents.current;
    const showLoadMore = !eventsLoading && events.length > 0 && eventPage < totalEventPages;

    return (
        <Box
            className="container"
            style={{
                minHeight: '100vh',
                display: 'flex',
                flexDirection: 'column',
                backgroundColor: '#f5f5f5',
                position: 'relative',
                overscrollBehavior: 'none',
                WebkitOverflowScrolling: 'touch',
                touchAction: 'pan-y',
            }}
        >
            <Box 
                style={{
                    position: 'sticky',
                    top: 0,
                    left: 0,
                    right: 0,
                    zIndex: 100,
                    backgroundColor: '#fff'
                }}
            >
                <HeaderEdu />
                <HeaderSpacer />
            </Box>
            
            <Box
                style={{
                    flex: 1,
                    paddingBottom: '60px',
                    overscrollBehavior: 'none',
                }}
            >
                <Box
                    className="quick-access"
                    style={{
                        padding: '15px',
                        display: 'grid',
                        gridTemplateColumns: 'repeat(4, 1fr)',
                        gap: '15px',
                        backgroundColor: 'white',
                    }}
                >
                    {[
                        { icon: '✓', text: 'Điểm danh', path: '/attendance' },
                        { icon: '📝', text: 'Bài tập', path: '/exams' },
                        { icon: '📊', text: 'Điểm số', path: '/grades' },
                        { icon: '📅', text: 'Lịch học', path: '/schedule' },
                    ].map((item, index) => (
                        <Box
                            key={index}
                            className="quick-item"
                            flex
                            flexDirection="column"
                            alignItems="center"
                            style={{ textAlign: 'center', cursor: 'pointer' }}
                            onClick={() => navigate(item.path)}
                        >
                            <Box
                                className="quick-icon"
                                style={{
                                    width: '50px',
                                    height: '50px',
                                    borderRadius: '12px',
                                    backgroundColor: '#e8f0fe',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    marginBottom: '8px',
                                    color: '#0068ff',
                                    fontSize: '22px',
                                }}
                            >
                                {item.icon}
                            </Box>
                            <Text className="quick-text" style={{ fontSize: '12px', color: '#666' }}>
                                {item.text}
                            </Text>
                        </Box>
                    ))}
                </Box>

                <Box
                    className="section"
                    style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}
                >
                    <Box
                        className="section-header"
                        flex
                        justifyContent="space-between"
                        alignItems="center"
                        style={{ marginBottom: '15px' }}
                    >
                        <Box flex alignItems="center">
                            <Text className="section-title" bold size="large">
                                Thông báo gần đây
                            </Text>
                            {unreadCount > 0 && (
                                <Box style={{
                                    backgroundColor: '#ff4444',
                                    color: 'white',
                                    fontSize: '12px',
                                    fontWeight: 'bold',
                                    minWidth: '20px',
                                    height: '20px',
                                    borderRadius: '10px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    marginLeft: '8px'
                                }}>
                                    {unreadCount}
                                </Box>
                            )}
                        </Box>
                        <Text
                            className="see-all"
                            style={{ color: '#0068ff', fontSize: '14px', cursor: 'pointer' }}
                            onClick={() => navigate('/announcements')}
                        >
                            Xem tất cả
                        </Text>
                    </Box>
                    <List
                        className="announcements-list"
                        style={{ display: 'flex', flexDirection: 'column', gap: '15px' }}
                    >
                        {announcementsLoading ? (
                            <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                                <LoadingIndicator />
                            </Box>
                        ) : recentAnnouncements.length > 0 ? (
                            recentAnnouncements.map((item) => (
                                <Box
                                    key={item.id}
                                    className="announcement-item"
                                    style={{
                                        display: 'flex',
                                        backgroundColor: item.isRead ? '#f9f9f9' : '#f0f8ff',
                                        borderRadius: '8px',
                                        padding: '12px',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                        cursor: 'pointer',
                                        borderLeft: item.isRead ? 'none' : '4px solid #0068ff'
                                    }}
                                    onClick={() => fetchAnnouncementDetail(item.id)}
                                >
                                    <Box
                                        className="announcement-icon"
                                        style={{
                                            width: '40px',
                                            height: '40px',
                                            borderRadius: '50%',
                                            backgroundColor: '#e8f0fe',
                                            color: '#0068ff',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            marginRight: '12px',
                                            fontSize: '20px'
                                        }}
                                    >
                                        📢
                                    </Box>
                                    <Box className="announcement-content" flex="1">
                                        <Text className="announcement-text" style={{ lineHeight: 1.4, marginBottom: '5px', fontWeight: item.isRead ? 'normal' : 'bold' }}>
                                            {item.title || item.content?.substring(0, 50) + (item.content?.length > 50 ? '...' : '')}
                                        </Text>
                                        <Text className="announcement-meta" style={{ fontSize: '12px', color: '#888' }}>
                                            {item.sender?.name} • {formatDistanceToNow(new Date(item.createdAt), { locale: vi, addSuffix: true })}
                                        </Text>
                                    </Box>
                                    {!item.isRead && (
                                        <Box style={{ width: '8px', height: '8px', borderRadius: '50%', backgroundColor: '#0068ff', alignSelf: 'center' }} />
                                    )}
                                </Box>
                            ))
                        ) : (
                            <Text style={{ textAlign: 'center', color: '#888', padding: '20px' }}>Chưa có thông báo mới</Text>
                        )}
                    </List>
                </Box>

                <Box 
                    className="section" 
                    style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}
                >
                    <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                        <Text className="section-title" bold size="large">
                            Sự kiện sắp tới
                        </Text>
                        <Box flex>
                            <Text
                                className="see-all"
                                style={{ color: '#0068ff', fontSize: '14px', cursor: 'pointer' }}
                                onClick={() => navigate('/all-events')}
                            >
                                Lịch đầy đủ
                            </Text>
                        </Box>
                    </Box>

                    {/* Hiển thị loading khi đang tải dữ liệu ban đầu */}
                    {showEventsLoading ? (
                        <Box style={{ padding: '20px 0', textAlign: 'center' }}>
                            <Loading />
                        </Box>
                    ) : (
                        <Box style={{ position: 'relative' }}>
                            <Box
                                className="upcoming-events"
                                ref={eventsContainerRef}
                                style={{
                                    display: 'flex',
                                    overflowX: 'auto',
                                    gap: '15px',
                                    paddingBottom: '15px',
                                    flexWrap: 'nowrap',
                                    scrollBehavior: 'smooth',
                                    WebkitOverflowScrolling: 'touch',
                                }}
                            >
                                {events.map((item, index) => (
                                    <Box
                                        key={item._id || `event-${index}`}
                                        className="event-card"
                                        style={{
                                            minWidth: '250px',
                                            backgroundColor: '#f0f6ff',
                                            borderRadius: '8px',
                                            padding: '12px',
                                            borderLeft: '4px solid #0068ff',
                                            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                            flexShrink: 0,
                                        }}
                                    >
                                        <Text className="event-date" bold style={{ color: '#0068ff', marginBottom: '5px' }}>
                                            {formatEventDate(item.startTime)}
                                        </Text>
                                        <Text className="event-title" bold style={{ marginBottom: '5px' }}>
                                            {item.title}
                                        </Text>
                                        <Text className="event-time" style={{ fontSize: '12px', color: '#666' }}>
                                            {formatEventTime(item.startTime, item.endTime, item.description)}
                                        </Text>

                                        {/* Gắn observer vào phần tử cuối cùng */}
                                        {index === events.length - 1 && eventPage < totalEventPages && (
                                            <div ref={observerRef} style={{ height: '1px', width: '100%' }} />
                                        )}
                                    </Box>
                                ))}

                                {/* Loading indicator khi đang tải thêm */}
                                {eventsLoading && events.length > 0 && (
                                    <Box
                                        style={{
                                            minWidth: '120px',
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            flexShrink: 0,
                                            padding: '12px',
                                            borderRadius: '8px',
                                            backgroundColor: '#f9f9f9',
                                        }}
                                    >
                                        <LoadingIndicator />
                                    </Box>
                                )}

                                {/* Nút "Xem thêm" */}
                                {showLoadMore && (
                                    <Box
                                        style={{
                                            minWidth: '120px',
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center',
                                            flexShrink: 0,
                                            padding: '12px',
                                            borderRadius: '8px',
                                            backgroundColor: '#f0f6ff',
                                            cursor: 'pointer',
                                        }}
                                        onClick={handleLoadMore}
                                    >
                                        <Text style={{ color: '#0068ff' }}>Xem thêm</Text>
                                    </Box>
                                )}

                                {/* Empty state */}
                                {showNoEvents && (
                                    <Box style={{ padding: '20px 0', minWidth: '100%', textAlign: 'center' }}>
                                        <Text>Chưa có sự kiện</Text>
                                    </Box>
                                )}
                            </Box>

                            {/* Nút mũi tên scroll */}
                            {events.length > 2 && (
                                <Box
                                    style={{
                                        position: 'absolute',
                                        right: 0,
                                        top: '40%',
                                        backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                        borderRadius: '50%',
                                        width: '36px',
                                        height: '36px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                                        cursor: 'pointer',
                                        zIndex: 1,
                                    }}
                                    onClick={() => {
                                        if (eventsContainerRef.current) {
                                            eventsContainerRef.current.scrollLeft += 300;
                                        }
                                    }}
                                >
                                    <Text style={{ fontSize: '20px' }}>→</Text>
                                </Box>
                            )}
                        </Box>
                    )}
                </Box>

                <NewsComponent />
            </Box>

            {/* Announcement Detail Modal */}
            <Modal
                visible={announcementDetailVisible}
                title="Chi tiết thông báo"
                onClose={() => {
                    setAnnouncementDetailVisible(false);
                    setSelectedAnnouncement(null);
                }}
                actions={[
                    { text: 'Đóng', close: true }
                ]}
            >
                {selectedAnnouncement && (
                    <Box p={4}>
                        <Box style={{ marginBottom: '15px' }}>
                            <Text bold size="large" style={{ marginBottom: '8px' }}>
                                {selectedAnnouncement.title || 'Thông báo'}
                            </Text>
                            <Box flex alignItems="center" style={{ marginBottom: '10px' }}>
                                <Text style={{ fontSize: '14px', color: '#666' }}>
                                    Từ: <Text bold>{selectedAnnouncement.sender?.name}</Text>
                                </Text>
                                <Text style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>
                                    {formatDistanceToNow(new Date(selectedAnnouncement.createdAt), { locale: vi, addSuffix: true })}
                                </Text>
                            </Box>
                            {selectedAnnouncement.type && (
                                <Box style={{
                                    backgroundColor: '#e8f0fe',
                                    color: '#0068ff',
                                    padding: '4px 8px',
                                    borderRadius: '4px',
                                    fontSize: '12px',
                                    display: 'inline-block',
                                    marginBottom: '10px'
                                }}>
                                    {selectedAnnouncement.type === 'teacher_to_student' && 'Giáo viên gửi học sinh'}
                                    {selectedAnnouncement.type === 'head_to_teacher' && 'Tổ trưởng gửi giáo viên'}
                                    {selectedAnnouncement.type === 'principal_to_teacher' && 'Hiệu trưởng gửi giáo viên'}
                                    {selectedAnnouncement.type === 'admin_to_all' && 'Ban giám hiệu gửi toàn trường'}
                                </Box>
                            )}
                        </Box>
                        <Box style={{
                            backgroundColor: '#f9f9f9',
                            padding: '15px',
                            borderRadius: '8px',
                            lineHeight: 1.6
                        }}>
                            <Text>{selectedAnnouncement.content}</Text>
                        </Box>
                        {selectedAnnouncement.zaloConfig?.enabled && (
                            <Box style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f0f8ff', borderRadius: '6px' }}>
                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                    📱 Đã gửi qua Zalo: {selectedAnnouncement.zaloConfig.groupName}
                                    {selectedAnnouncement.zaloConfig.sentAt && (
                                        <Text> • {formatDistanceToNow(new Date(selectedAnnouncement.zaloConfig.sentAt), { locale: vi, addSuffix: true })}</Text>
                                    )}
                                </Text>
                            </Box>
                        )}
                    </Box>
                )}
            </Modal>

            <Box
                style={{
                    position: 'fixed',
                    bottom: 0,
                    left: 0,
                    right: 0,
                    zIndex: 100,
                    backgroundColor: '#fff',
                    borderTop: '1px solid #eee'
                }}
            >
                <BottomNavigationEdu />
            </Box>
        </Box>
    );
};

export default StudentEdu;