import React, { useEffect, useState, useRef, useContext, useCallback } from 'react';
import { Box, Text, List, Button, useNavigate, Modal, Select, Input, Checkbox } from 'zmp-ui';
import HeaderEdu from '../components/HeaderEdu';
import HeaderSpacer from '../components/HeaderSpacer';
import BottomNavigationEdu from '../components/BottomNavigationEdu';
import { AuthContext } from '../context/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import vi from 'date-fns/locale/vi';
import NewsComponent from '../components/NewsComponent';
import { authApi } from '../utils/api';
import LoadingIndicator from '../components/LoadingIndicator';
import ConfirmModal from '../components/ConfirmModal';
import useApiCache from '../hooks/useApiCache';

const { Option } = Select;

const TeacherEdu = () => {
    const navigate = useNavigate();
    const { user, classId, loading: authLoading } = useContext(AuthContext);
    const [classesLoading, setClassesLoading] = useState(true);
    const [tasksLoading, setTasksLoading] = useState(true);
    const [eventsLoading, setEventsLoading] = useState(false);
    const [notificationsLoading, setNotificationsLoading] = useState(true);
    const [classes, setClasses] = useState([]);
    const [tasks, setTasks] = useState([]);
    const [events, setEvents] = useState([]);
    const [eventPage, setEventPage] = useState(1);
    const [totalEventPages, setTotalEventPages] = useState(1);
    const [teacherClasses, setTeacherClasses] = useState([]);
    const [weeklySchedule, setWeeklySchedule] = useState([]);

    // Announcement modal states
    const [selectedAnnouncement, setSelectedAnnouncement] = useState(null);
    const [announcementDetailVisible, setAnnouncementDetailVisible] = useState(false);

    // Trạng thái cho modal
    const [modalVisible, setModalVisible] = useState(false);
    const [formData, setFormData] = useState({
        type: 'teacher_to_student',
        title: '',
        content: '',
        recipients: { class: '', department: '' },
        zaloConfig: { enabled: false, groupId: '', groupName: '', scheduledTime: '' },
    });
    const [error, setError] = useState('');
    const [submitting, setSubmitting] = useState(false);

    // API data states
    const [availableClasses, setAvailableClasses] = useState([]);
    const [announcementConfig, setAnnouncementConfig] = useState(null);
    const [zaloGroups, setZaloGroups] = useState([]);
    const [loadingClasses, setLoadingClasses] = useState(false);
    const [loadingConfig, setLoadingConfig] = useState(false);
    const [loadingZaloGroups, setLoadingZaloGroups] = useState(false);

    // Confirm modal state
    const [confirmModal, setConfirmModal] = useState({
        visible: false,
        title: '',
        message: '',
        onConfirm: null,
        confirmText: 'Xác nhận',
        cancelText: 'Hủy'
    });

    // Khai báo useRef cho events
    const eventsContainerRef = useRef(null);
    const observerRef = useRef(null);
    const hasLoadedInitialEvents = useRef(false);
    const isLoadingMore = useRef(false);
    const loadedEventIds = useRef(new Set());

    // Helper function to get current week date range
    const getCurrentWeekRange = useCallback(() => {
        const today = new Date();
        const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
        const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay;

        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() + mondayOffset);
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);

        return {
            startDate: startOfWeek.toISOString().split('T')[0],
            endDate: endOfWeek.toISOString().split('T')[0],
            weekText: `${startOfWeek.getDate()}/${startOfWeek.getMonth() + 1} - ${endOfWeek.getDate()}/${endOfWeek.getMonth() + 1}`
        };
    }, []);

    // API functions
    const fetchClasses = useCallback(async () => {
        if (!user) return;
        setLoadingClasses(true);
        try {
            const response = await authApi.get('/directory/classes?schoolYear=2024-2025');
            if (response.data.success) {
                setAvailableClasses(response.data.data || []);
            }
        } catch (err) {
            console.error('Error fetching classes:', err);
            setError('Lỗi khi tải danh sách lớp học');
        } finally {
            setLoadingClasses(false);
        }
    }, [user]);

    // Fetch teacher's classes with attendance statistics
    const fetchTeacherClasses = useCallback(async () => {
        if (!user) return;
        setClassesLoading(true);
        try {
            const response = await authApi.get('/directory/teacher/classes');
            if (response.data.success) {
                const classesData = response.data.data || [];

                // Fetch attendance statistics for each class
                const classesWithAttendance = await Promise.all(
                    classesData.map(async (classItem) => {
                        try {
                            // Get current week date range
                            const today = new Date();
                            const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
                            const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay;

                            const startOfWeek = new Date(today);
                            startOfWeek.setDate(today.getDate() + mondayOffset);
                            const endOfWeek = new Date(startOfWeek);
                            endOfWeek.setDate(startOfWeek.getDate() + 6);

                            const startDate = startOfWeek.toISOString().split('T')[0];
                            const endDate = endOfWeek.toISOString().split('T')[0];
                            const session = new Date().getHours() < 12 ? 'morning' : 'afternoon';

                            const attendanceResponse = await authApi.get(
                                `/attendance/statistics/class/${classItem.id}?startDate=${startDate}&endDate=${endDate}&session=${session}`
                            );

                            if (attendanceResponse.data.success) {
                                const attendanceData = attendanceResponse.data.data;
                                const overview = attendanceData.overview;
                                const totalStudents = attendanceData.class.totalStudents || classItem.studentCount || 0;
                                const attendedStudents = overview.studentsAttended || 0;
                                const attendanceRate = totalStudents > 0 ? Math.round((attendedStudents / totalStudents) * 100) : 0;

                                return {
                                    ...classItem,
                                    attendance: `${attendedStudents}/${totalStudents}`,
                                    progress: `${attendanceRate}%`,
                                    subjects: classItem.teachingSubjects?.join(', ') || '',
                                    periods: classItem.teachingPeriods?.length > 0 ?
                                        `Tiết ${classItem.teachingPeriods.map(p => p.periodNumber).join(',')}` : 'Chưa có lịch',
                                    attendanceStats: {
                                        totalRecords: overview.totalAttendanceRecords || 0,
                                        present: overview.studentsAttended || 0,
                                        absent: overview.studentsNotAttended || 0,
                                        rate: attendanceRate
                                    }
                                };
                            } else {
                                // Fallback if statistics API fails
                                return {
                                    ...classItem,
                                    attendance: '0/0',
                                    progress: '0%',
                                    subjects: classItem.teachingSubjects?.join(', ') || '',
                                    periods: 'Chưa có lịch',
                                    attendanceStats: {
                                        totalRecords: 0,
                                        present: 0,
                                        absent: 0,
                                        rate: 0
                                    }
                                };
                            }
                        } catch (err) {
                            console.error(`Error fetching attendance statistics for class ${classItem.id}:`, err);
                            return {
                                ...classItem,
                                attendance: '0/0',
                                progress: '0%',
                                subjects: classItem.teachingSubjects?.join(', ') || '',
                                periods: 'Chưa có lịch',
                                attendanceStats: {
                                    totalRecords: 0,
                                    present: 0,
                                    absent: 0,
                                    rate: 0
                                }
                            };
                        }
                    })
                );

                setTeacherClasses(classesWithAttendance);
                // Set limited classes for display (max 4)
                setClasses(classesWithAttendance.slice(0, 4).map(cls => ({
                    _id: cls.id,
                    name: `${cls.name} - ${cls.subjects}`,
                    students: cls.studentCount,
                    periods: cls.periods,
                    attendance: cls.attendance,
                    progress: cls.progress,
                    attendanceStats: cls.attendanceStats
                })));
            }
        } catch (err) {
            console.error('Error fetching teacher classes:', err);
            setError('Lỗi khi tải danh sách lớp học');
        } finally {
            setClassesLoading(false);
        }
    }, [user]);

    const fetchAnnouncementConfig = useCallback(async () => {
        setLoadingConfig(true);
        try {
            const response = await authApi.get('/announcements/config');
            setAnnouncementConfig(response.data);
        } catch (err) {
            console.error('Error fetching announcement config:', err);
            setError('Lỗi khi tải cấu hình thông báo');
        } finally {
            setLoadingConfig(false);
        }
    }, []);

    const fetchZaloGroups = useCallback(async () => {
        if (!user) return;
        setLoadingZaloGroups(true);
        try {
            let response;
            if (user.role === 'admin') {
                response = await authApi.get('/zalo/groups?count=20&offset=0');
                setZaloGroups(response.data.data?.groups || []);
            } else {
                response = await authApi.get('/zalo/user/groups');
                setZaloGroups(response.data.user?.groupZaloIds || []);
            }
        } catch (err) {
            console.error('Error fetching Zalo groups:', err);
            setError('Lỗi khi tải danh sách nhóm Zalo');
        } finally {
            setLoadingZaloGroups(false);
        }
    }, [user]);

    // API functions for useApiCache
    const fetchRecentAnnouncements = useCallback(async () => {
        if (!user) return [];
        const response = await authApi.get('/announcements/recent?limit=5');
        return Array.isArray(response.data) ? response.data : [];
    }, [user]);

    const fetchUnreadCount = useCallback(async () => {
        if (!user) return 0;
        const response = await authApi.get('/announcements/unread?limit=10');
        return response.data.success ? (response.data.data.unreadCount || 0) : 0;
    }, [user]);

    // Use useApiCache for announcements
    const {
        data: recentAnnouncements = [],
        loading: announcementsLoading,
        refetch: refetchAnnouncements
    } = useApiCache(fetchRecentAnnouncements, [user?._id], {
        cacheKey: `teacher_announcements_${user?._id}`,
        enabled: !!user,
        cacheTime: 2 * 60 * 1000, // Cache 2 phút
    });

    const {
        data: unreadCount = 0,
        refetch: refetchUnreadCount
    } = useApiCache(fetchUnreadCount, [user?._id], {
        cacheKey: `teacher_unread_count_${user?._id}`,
        enabled: !!user,
        cacheTime: 1 * 60 * 1000, // Cache 1 phút
    });

    // Combined refresh function
    const refreshAnnouncementData = useCallback(() => {
        refetchAnnouncements();
        refetchUnreadCount();
    }, [refetchAnnouncements, refetchUnreadCount]);

    // Mark announcement as read
    const markAsRead = useCallback(async (announcementId) => {
        try {
            await authApi.post(`/announcements/${announcementId}/read`);
            // Refresh data to get updated state
            refreshAnnouncementData();
        } catch (err) {
            console.error('Error marking announcement as read:', err);
        }
    }, [refreshAnnouncementData]);

    // Handle announcement click
    const handleAnnouncementClick = useCallback(async (announcement) => {
        // Mark as read immediately for better UX
        if (!announcement.isRead) {
            markAsRead(announcement.id);
        }

        // Then fetch and show detail
        try {
            const response = await authApi.get(`/announcements/${announcement.id}`);
            setSelectedAnnouncement(response.data);
            setAnnouncementDetailVisible(true);
        } catch (err) {
            console.error('Error fetching announcement detail:', err);
            // Fallback to show current data
            setSelectedAnnouncement(announcement);
            setAnnouncementDetailVisible(true);
        }
    }, [markAsRead]);

    // Fetch announcement detail (legacy - keep for compatibility)
    const fetchAnnouncementDetail = useCallback(async (announcementId) => {
        try {
            const response = await authApi.get(`/announcements/${announcementId}`);
            setSelectedAnnouncement(response.data);
            setAnnouncementDetailVisible(true);
            // Mark as read when viewing detail
            if (!response.data.isRead) {
                markAsRead(announcementId);
            }
        } catch (err) {
            console.error('Error fetching announcement detail:', err);
        }
    }, [markAsRead]);

    // Show confirm modal function
    const showConfirmModal = (title, message, onConfirm, confirmText = 'Xác nhận', cancelText = 'Hủy') => {
        setConfirmModal({
            visible: true,
            title,
            message,
            onConfirm,
            confirmText,
            cancelText
        });
    };

    // Hide confirm modal function
    const closeConfirmModal = () => {
        setConfirmModal(prev => ({
            ...prev,
            visible: false
        }));
    };

    // Kiểm tra xác thực
    useEffect(() => {
        if (!authLoading && !user) {
            navigate('/login', { replace: true });
        } else if (user && user.role !== 'teacher' && user.role !== 'TEACHER') {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            navigate('/login', { replace: true });
        }
    }, [user, authLoading, navigate]);

    // Load initial data when modal opens
    useEffect(() => {
        if (modalVisible && user) {
            fetchAnnouncementConfig();
        }
    }, [modalVisible, user, fetchAnnouncementConfig]);

    // Load classes when type changes to teacher_to_student
    useEffect(() => {
        if (modalVisible && user && formData.type === 'teacher_to_student') {
            fetchClasses();
        }
    }, [modalVisible, user, formData.type, fetchClasses]);

    // Load Zalo groups when Zalo is enabled
    useEffect(() => {
        if (formData.zaloConfig.enabled && user) {
            fetchZaloGroups();
        }
    }, [formData.zaloConfig.enabled, user, fetchZaloGroups]);

    // Generate weekly schedule from teacher classes
    const generateWeeklySchedule = useCallback(() => {
        if (!teacherClasses.length) return;

        const today = new Date();
        const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
        const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Calculate Monday of current week

        const weekDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        const weekDaysVN = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];

        const weekSchedule = weekDaysVN.map((dayVN, index) => {
            const dayEn = weekDays[index];
            const date = new Date(today);
            date.setDate(today.getDate() + mondayOffset + index);

            // Check if any class has teaching periods on this day
            const hasClass = teacherClasses.some(cls =>
                cls.teachingPeriods?.some(period => period.day === dayEn)
            );

            return {
                day: dayVN,
                number: date.getDate().toString(),
                today: date.toDateString() === today.toDateString(),
                hasClass: hasClass
            };
        });

        setWeeklySchedule(weekSchedule);
    }, [teacherClasses]);

    // Refresh all data
    const refreshData = useCallback(() => {
        if (user && (user.role === 'teacher' || user.role === 'TEACHER')) {
            fetchTeacherClasses();
            refreshAnnouncementData();
        }
    }, [user, fetchTeacherClasses, refreshAnnouncementData]);

    // Load teacher data
    useEffect(() => {
        refreshData();
    }, [refreshData]);

    // Generate weekly schedule when teacher classes are loaded
    useEffect(() => {
        generateWeeklySchedule();
    }, [generateWeeklySchedule]);

    useEffect(() => {
        setTasks([
            { title: 'Bài tập cần chấm', meta: '10A1 - Hạn: Hôm nay', count: 32 },
            { title: 'Cập nhật điểm giữa kỳ', meta: '12A3 - Hạn: 25/04', count: 38 },
            { title: 'Tin nhắn phụ huynh', meta: 'Cần phản hồi', count: 5 },
        ]);
        setTasksLoading(false);
    }, []);

    // Xử lý thay đổi form
    const handleFormChange = (field, value) => {
        setFormData((prev) => {
            let updatedFormData;
            switch (field) {
                case 'type':
                    updatedFormData = {
                        ...prev,
                        type: value,
                        recipients: { class: '', department: '' },
                        zaloConfig: { ...prev.zaloConfig, groupId: '', groupName: '' },
                    };
                    break;
                case 'class':
                    updatedFormData = {
                        ...prev,
                        recipients: { ...prev.recipients, class: value },
                        zaloConfig: { ...prev.zaloConfig, groupId: '', groupName: '' },
                    };
                    break;
                case 'department':
                    updatedFormData = {
                        ...prev,
                        recipients: { ...prev.recipients, department: value },
                    };
                    break;
                case 'zaloEnabled':
                    updatedFormData = {
                        ...prev,
                        zaloConfig: { ...prev.zaloConfig, enabled: value, groupId: '', groupName: '', scheduledTime: '' },
                    };
                    break;
                case 'scheduledTime':
                    updatedFormData = {
                        ...prev,
                        zaloConfig: { ...prev.zaloConfig, scheduledTime: value },
                    };
                    break;
                case 'groupId':
                    const selectedGroup = zaloGroups.find(group =>
                        (user.role === 'admin' ? group.group_id : group.groupId) === value
                    );
                    updatedFormData = {
                        ...prev,
                        zaloConfig: {
                            ...prev.zaloConfig,
                            groupId: value,
                            groupName: selectedGroup ? (user.role === 'admin' ? selectedGroup.name : selectedGroup.groupName) : ''
                        },
                    };
                    break;
                case 'title':
                    updatedFormData = { ...prev, title: value };
                    break;
                case 'content':
                    updatedFormData = { ...prev, content: value };
                    break;
                default:
                    updatedFormData = prev;
            }
            return updatedFormData;
        });
        setError('');
    };

    // Xử lý gửi thông báo
    const handleSubmit = async () => {
        // Validation
        if (!formData.content) {
            setError('Vui lòng nhập nội dung thông báo');
            return;
        }

        // Validate based on announcement type
        if (formData.type === 'teacher_to_student' && !formData.recipients.class) {
            setError('Vui lòng chọn lớp học để gửi thông báo cho học sinh');
            return;
        }

        if (formData.type === 'head_to_teacher' && !formData.recipients.department) {
            setError('Vui lòng chọn bộ môn để gửi thông báo cho giáo viên');
            return;
        }

        if (formData.zaloConfig.enabled && !formData.zaloConfig.groupId) {
            setError('Vui lòng chọn nhóm Zalo');
            return;
        }

        if (formData.zaloConfig.enabled && formData.zaloConfig.scheduledTime && new Date(formData.zaloConfig.scheduledTime) < new Date()) {
            setError('Thời gian lên lịch phải là trong tương lai');
            return;
        }

        setSubmitting(true);
        setError('');

        try {
            // Prepare request body
            const requestBody = {
                type: formData.type,
                title: formData.title,
                content: formData.content,
                recipients: {},
                status: formData.zaloConfig.enabled && formData.zaloConfig.scheduledTime ? 'scheduled' : 'sent'
            };

            // Set recipients based on type
            switch (formData.type) {
                case 'teacher_to_student':
                    requestBody.recipients = {
                        class: formData.recipients.class,
                        schoolWide: false
                    };
                    break;
                case 'principal_to_teacher':
                case 'admin_to_all':
                    requestBody.recipients = {
                        schoolWide: true,
                        teachers: [],
                        department: null
                    };
                    break;
                case 'head_to_teacher':
                    requestBody.recipients = {
                        schoolWide: false,
                        teachers: [],
                        department: formData.recipients.department
                    };
                    break;
            }

            // Add Zalo config if enabled
            if (formData.zaloConfig.enabled) {
                requestBody.zaloConfig = {
                    enabled: true,
                    groupId: formData.zaloConfig.groupId,
                    groupName: formData.zaloConfig.groupName,
                    scheduledTime: formData.zaloConfig.scheduledTime || null,
                    sent: false
                };
            }

            await authApi.post('/announcements', requestBody);

            // Success
            setModalVisible(false);
            setFormData({
                type: 'teacher_to_student',
                title: '',
                content: '',
                recipients: { class: '', department: '' },
                zaloConfig: { enabled: false, groupId: '', groupName: '', scheduledTime: '' },
            });

            // Refresh announcement data
            refreshAnnouncementData();

            // Show success message
            showConfirmModal(
                'Thành công',
                `Thông báo đã được ${formData.zaloConfig.scheduledTime ? 'lên lịch' : 'gửi'} thành công!`,
                closeConfirmModal,
                'Đóng',
                ''
            );

        } catch (err) {
            console.error('Error sending announcement:', err);
            const errorMsg = err.response?.data?.msg || 'Có lỗi xảy ra khi gửi thông báo';

            // Xác định loại lỗi để hiển thị title phù hợp
            let errorTitle = 'Lỗi gửi thông báo';
            if (errorMsg.includes('quyền')) {
                errorTitle = 'Không có quyền';
            } else if (errorMsg.includes('lịch')) {
                errorTitle = 'Lỗi lên lịch';
            } else if (errorMsg.includes('Zalo')) {
                errorTitle = 'Lỗi Zalo';
            }

            // Hiển thị lỗi qua confirm modal thay vì setError
            showConfirmModal(
                errorTitle,
                errorMsg,
                closeConfirmModal,
                'Đóng',
                ''
            );
        } finally {
            setSubmitting(false);
        }
    };

    // Giả lập dữ liệu sự kiện
    const fetchEvents = useCallback(() => {
        setEvents([
            { _id: '1', title: 'Buổi họp lớp 10A1', date: new Date() },
            { _id: '2', title: 'Kiểm tra Toán 11A2', date: new Date() },
        ]);
        setTotalEventPages(1);
        setEventsLoading(false);
    }, []);

    // Tải sự kiện ban đầu
    useEffect(() => {
        if (user && !hasLoadedInitialEvents.current) {
            fetchEvents();
            hasLoadedInitialEvents.current = true;
        }
    }, [user, fetchEvents]);

    // Xử lý scroll và IntersectionObserver
    const handleScroll = useCallback(() => {
        if (isLoadingMore.current || eventPage >= totalEventPages) return;

        const container = eventsContainerRef.current;
        if (!container) return;

        const scrollPosition = container.scrollLeft;
        const containerWidth = container.clientWidth;
        const scrollWidth = container.scrollWidth;

        const isNearEnd = scrollWidth - (scrollPosition + containerWidth) < 100;

        if (isNearEnd && !isLoadingMore.current && eventPage < totalEventPages) {
            setEventPage((prev) => prev + 1);
        }
    }, [eventPage, totalEventPages]);

    useEffect(() => {
        if (!observerRef.current || isLoadingMore.current || eventPage >= totalEventPages) return;

        const observer = new IntersectionObserver(
            (entries) => {
                const entry = entries[0];
                if (entry.isIntersecting && !isLoadingMore.current && eventPage < totalEventPages) {
                    setEventPage((prev) => prev + 1);
                }
            },
            { threshold: 0.5 }
        );

        const currentRef = observerRef.current;
        if (currentRef) {
            observer.observe(currentRef);
        }

        return () => {
            if (currentRef) {
                observer.unobserve(currentRef);
            }
            observer.disconnect();
        };
    }, [eventPage, totalEventPages]);

    useEffect(() => {
        const container = eventsContainerRef.current;
        if (!container || eventPage >= totalEventPages) return;

        let isThrottled = false;
        const throttledScroll = () => {
            if (!isThrottled) {
                handleScroll();
                isThrottled = true;
                setTimeout(() => {
                    isThrottled = false;
                }, 300);
            }
        };

        container.addEventListener('scroll', throttledScroll);
        return () => container.removeEventListener('scroll', throttledScroll);
    }, [handleScroll, eventPage, totalEventPages]);

    return (
        <Box className="container" style={{ minHeight: '100vh', display: 'flex', flexDirection: 'column', paddingBottom: '60px', backgroundColor: '#f5f5f5' }}>
            <HeaderEdu />
            <HeaderSpacer />
            {/* Quick Access */}
            <Box className="quick-access" style={{ padding: '15px', display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '15px', backgroundColor: 'white' }}>
                {[
                    { icon: '✓', text: 'Điểm danh', path: '/teacher-attendance' },
                    { icon: '📝', text: 'Giao bài', path: '/teacher-assignments' },
                    { icon: '📊', text: 'Chấm bài', path: '/teacher-grading' },
                    { icon: '📅', text: 'Lịch dạy', path: '/teacher-schedule' },
                ].map((item, index) => (
                    <Box key={index} className="quick-item" flex flexDirection="column" alignItems="center" style={{ textAlign: 'center', cursor: 'pointer' }} onClick={() => navigate(item.path)}>
                        <Box className="quick-icon" style={{ width: '50px', height: '50px', borderRadius: '12px', backgroundColor: '#e8f0fe', display: 'flex', alignItems: 'center', justifyContent: 'center', marginBottom: '8px', color: '#0068ff', fontSize: '22px' }}>
                            {item.icon}
                        </Box>
                        <Text className="quick-text" style={{ fontSize: '12px', color: '#666' }}>{item.text}</Text>
                    </Box>
                ))}
            </Box>
            {/* Classes */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text className="section-title" bold size="large">Lớp học của tôi</Text>
                    <Box flex alignItems="center" style={{ gap: '10px' }}>
                        <Text
                            style={{ color: '#666', fontSize: '12px', cursor: 'pointer' }}
                            onClick={refreshData}
                        >
                            🔄
                        </Text>
                        <Text
                            className="see-all"
                            style={{ color: '#0068ff', fontSize: '14px', cursor: 'pointer' }}
                            onClick={() => navigate('/teacher-classes')}
                        >
                            Xem tất cả
                        </Text>
                    </Box>
                </Box>
                <Box className="classes-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '12px' }}>
                    {classesLoading ? (
                        // Loading skeleton
                        Array.from({ length: 4 }).map((_, index) => (
                            <Box key={index} className="class-card" style={{ backgroundColor: '#f0f6ff', borderRadius: '10px', padding: '15px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                                <Box style={{ height: '20px', backgroundColor: '#e0e0e0', borderRadius: '4px', marginBottom: '10px' }} />
                                <Box style={{ height: '14px', backgroundColor: '#e0e0e0', borderRadius: '4px', marginBottom: '8px', width: '70%' }} />
                                <Box style={{ height: '14px', backgroundColor: '#e0e0e0', borderRadius: '4px', marginBottom: '8px', width: '50%' }} />
                                <Box style={{ height: '6px', backgroundColor: '#e0e0e0', borderRadius: '3px' }} />
                            </Box>
                        ))
                    ) : classes.length > 0 ? (
                        classes.map((item, index) => (
                            <Box key={index} className="class-card" style={{ backgroundColor: '#f0f6ff', borderRadius: '10px', padding: '15px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                                <Text className="class-name" bold size="large" style={{ marginBottom: '8px' }}>{item.name}</Text>
                                <Box className="class-details" flex justifyContent="space-between" style={{ fontSize: '12px', color: '#666', marginBottom: '10px' }}>
                                    <Text>{item.students} học sinh</Text>
                                    <Text>{item.periods}</Text>
                                </Box>
                                <Box style={{ marginBottom: '8px' }}>
                                    <Text style={{ fontSize: '14px', marginBottom: '4px' }}>Điểm danh tuần: {item.attendance}</Text>
                                    {item.attendanceStats && (
                                        <Text style={{ fontSize: '12px', color: '#666' }}>
                                            {item.attendanceStats.totalRecords} buổi học • {item.attendanceStats.present} có mặt • {item.attendanceStats.absent} vắng
                                        </Text>
                                    )}
                                </Box>
                                <Box className="attendance-bar" style={{ height: '6px', backgroundColor: '#e0e0e0', borderRadius: '3px', position: 'relative' }}>
                                    <Box className="attendance-progress" style={{
                                        width: item.progress,
                                        height: '100%',
                                        backgroundColor: item.attendanceStats?.rate >= 80 ? '#4CAF50' :
                                                       item.attendanceStats?.rate >= 60 ? '#FF9800' : '#F44336',
                                        borderRadius: '3px'
                                    }} />
                                </Box>
                                <Text style={{ fontSize: '11px', color: '#666', marginTop: '4px', textAlign: 'right' }}>
                                    {item.progress}
                                </Text>
                            </Box>
                        ))
                    ) : (
                        <Box style={{ gridColumn: '1 / -1', textAlign: 'center', padding: '20px', color: '#666' }}>
                            <Text>Chưa có lớp học nào</Text>
                        </Box>
                    )}
                </Box>
            </Box>
            {/* Tasks */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text className="section-title" bold size="large">Nhiệm vụ cần xử lý</Text>
                    <Text className="see-all" style={{ color: '#0068ff', fontSize: '14px' }}>Xem tất cả</Text>
                </Box>
                <List className="tasks-list" style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {tasks.map((item, index) => (
                        <Box key={index} className="task-item" style={{ display: 'flex', alignItems: 'center', backgroundColor: '#f9f9f9', borderRadius: '8px', padding: '12px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                            <Box className="task-icon" style={{ width: '36px', height: '36px', borderRadius: '50%', backgroundColor: '#fef4e6', color: '#ff9500', display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: '12px', fontSize: '18px' }}>
                                📝
                            </Box>
                            <Box className="task-info" flex="1">
                                <Text className="task-title" bold>{item.title}</Text>
                                <Text className="task-meta" style={{ fontSize: '12px', color: '#888' }}>{item.meta}</Text>
                            </Box>
                            <Box className="task-count" style={{ backgroundColor: '#0068ff', color: 'white', fontSize: '12px', fontWeight: 'bold', minWidth: '24px', height: '24px', borderRadius: '12px', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '0 8px' }}>
                                {item.count}
                            </Box>
                        </Box>
                    ))}
                </List>
            </Box>
            {/* Student Summary */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Box flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text className="section-title" bold size="large">Tổng quan học sinh</Text>
                    <Text style={{ fontSize: '12px', color: '#666' }}>
                        {(() => {
                            const { weekText } = getCurrentWeekRange();
                            return `Tuần ${weekText}`;
                        })()}
                    </Text>
                </Box>
                <Box className="teacher-details" style={{ display: 'flex', justifyContent: 'space-between', backgroundColor: '#f9f9f9', borderRadius: '8px', padding: '15px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
                    {(() => {
                        // Calculate totals from all classes
                        const totals = teacherClasses.reduce((acc, cls) => {
                            const stats = cls.attendanceStats || {};
                            acc.totalStudents += cls.studentCount || 0;
                            acc.present += stats.present || 0;
                            acc.absent += stats.absent || 0;
                            acc.totalRecords += stats.totalRecords || 0;
                            return acc;
                        }, { totalStudents: 0, present: 0, absent: 0, totalRecords: 0 });

                        const attendanceRate = totals.totalStudents > 0 ?
                            Math.round((totals.present / totals.totalStudents) * 100) : 0;

                        return [
                            { value: totals.totalStudents.toString(), label: 'Tổng số' },
                            { value: totals.present.toString(), label: 'Đi học' },
                            { value: totals.absent.toString(), label: 'Vắng' },
                            { value: `${attendanceRate}%`, label: 'Tỷ lệ' },
                        ];
                    })().map((item, index) => (
                        <Box key={index} className="summary-item" flex flexDirection="column" alignItems="center" style={{ textAlign: 'center' }}>
                            <Text className="summary-value" bold style={{ fontSize: '20px', color: '#0068ff' }}>{item.value}</Text>
                            <Text className="summary-label" style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>{item.label}</Text>
                        </Box>
                    ))}
                </Box>
            </Box>
            {/* Calendar */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Text className="section-title" bold size="large">Lịch dạy trong tuần</Text>
                    <Text
                        className="see-all"
                        style={{ color: '#0068ff', fontSize: '14px', cursor: 'pointer' }}
                        onClick={() => navigate('/teacher-schedule')}
                    >
                        Tháng
                    </Text>
                </Box>
                <Box className="calendar-view" flex style={{ overflowX: 'auto', gap: '10px', paddingBottom: '10px' }}>
                    {weeklySchedule.map((item, index) => (
                        <Box key={index} className={`calendar-day ${item.today ? 'today' : ''} ${item.hasClass ? 'has-class' : ''}`} flex flexDirection="column" alignItems="center" style={{ padding: '10px', minWidth: '60px' }}>
                            <Text className="day-name" style={{ fontSize: '12px', color: '#666', marginBottom: '5px' }}>{item.day}</Text>
                            <Box className="day-number" style={{ width: '36px', height: '36px', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 'bold', backgroundColor: item.today ? '#0068ff' : 'transparent', color: item.today ? 'white' : 'black', border: item.hasClass ? '2px solid #0068ff' : 'none' }}>
                                {item.number}
                            </Box>
                            {item.hasClass && <Box className="day-indicator" style={{ width: '6px', height: '6px', borderRadius: '50%', backgroundColor: '#ff9500', marginTop: '5px' }} />}
                        </Box>
                    ))}
                </Box>
            </Box>
            {/* Notifications */}
            <Box className="section" style={{ marginTop: '15px', backgroundColor: 'white', padding: '15px' }}>
                <Box className="section-header" flex justifyContent="space-between" alignItems="center" style={{ marginBottom: '15px' }}>
                    <Box flex alignItems="center">
                        <Text className="section-title" bold size="large">Thông báo gần đây</Text>
                        {unreadCount > 0 && (
                            <Box style={{
                                backgroundColor: '#ff4444',
                                color: 'white',
                                fontSize: '12px',
                                fontWeight: 'bold',
                                minWidth: '20px',
                                height: '20px',
                                borderRadius: '10px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                marginLeft: '8px'
                            }}>
                                {unreadCount}
                            </Box>
                        )}
                    </Box>
                    <Text
                        className="see-all"
                        style={{ color: '#0068ff', fontSize: '14px', cursor: 'pointer' }}
                        onClick={() => navigate('/announcements')}
                    >
                        Xem tất cả
                    </Text>
                </Box>
                <List className="notification-list" style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {announcementsLoading ? (
                        <Box style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                            <LoadingIndicator />
                        </Box>
                    ) : recentAnnouncements.length > 0 ? (
                        recentAnnouncements.map((item) => (
                            <Box
                                key={item.id}
                                className="notification-item"
                                style={{
                                    display: 'flex',
                                    backgroundColor: item.isRead ? '#f9f9f9' : '#f0f8ff',
                                    borderRadius: '8px',
                                    padding: '12px',
                                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                                    cursor: 'pointer',
                                    borderLeft: item.isRead ? 'none' : '4px solid #0068ff'
                                }}
                                onClick={() => handleAnnouncementClick(item)}
                            >
                                <Box className="notification-icon" style={{ width: '40px', height: '40px', borderRadius: '50%', backgroundColor: '#e8f0fe', color: '#0068ff', display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: '12px', fontSize: '20px' }}>
                                    📢
                                </Box>
                                <Box className="notification-content" flex="1">
                                    <Text className="notification-text" style={{
                                        lineHeight: 1.4,
                                        marginBottom: '5px',
                                        fontWeight: !item.isRead ? 'bold' : 'normal',
                                        color: !item.isRead ? '#000' : '#333'
                                    }}>
                                        {item.title || item.content?.substring(0, 50) + (item.content?.length > 50 ? '...' : '')}
                                    </Text>
                                    <Text className="notification-meta" style={{
                                        fontSize: '12px',
                                        color: !item.isRead ? '#666' : '#888',
                                        fontWeight: !item.isRead ? '500' : 'normal'
                                    }}>
                                        {item.sender?.name} • {formatDistanceToNow(new Date(item.createdAt), { locale: vi, addSuffix: true })}
                                    </Text>
                                </Box>
                                {!item.isRead && (
                                    <Box style={{ width: '8px', height: '8px', borderRadius: '50%', backgroundColor: '#0068ff', alignSelf: 'center' }} />
                                )}
                            </Box>
                        ))
                    ) : (
                        <Text style={{ textAlign: 'center', color: '#888', padding: '20px' }}>Chưa có thông báo mới</Text>
                    )}
                </List>
            </Box>
            {/* Modal tạo thông báo */}
            <Modal
                visible={modalVisible}
                title="Tạo thông báo mới"
                onClose={() => {
                    setModalVisible(false);
                    setError('');
                    setFormData({
                        type: 'teacher_to_student',
                        content: '',
                        recipients: { classId: '', department: '' },
                        zaloConfig: { enabled: false, groupId: '', groupName: '', scheduledTime: '' },
                    });
                }}
                actions={[
                    { text: 'Hủy', close: true, danger: true },
                    { text: submitting ? 'Đang gửi...' : (formData.zaloConfig.scheduledTime ? 'Lên lịch' : 'Gửi ngay'), close: false, onClick: handleSubmit, disabled: submitting },
                ]}
            >
                <Box p={4}>
                    {error && <Text style={{ color: 'red', marginBottom: '10px' }}>{error}</Text>}

                    {/* Loại thông báo */}
                    <Text style={{ marginBottom: '5px' }}>Loại thông báo</Text>
                    {loadingConfig ? (
                        <Box style={{ display: 'flex', justifyContent: 'center', padding: '10px' }}>
                            <LoadingIndicator />
                        </Box>
                    ) : (
                        <Select
                            placeholder="Chọn loại thông báo"
                            value={formData.type}
                            onChange={(value) => handleFormChange('type', value)}
                            style={{ marginBottom: '15px' }}
                        >
                            {announcementConfig ? (
                                Object.entries(announcementConfig.typeLabels || {}).map(([key, label]) => (
                                    <Option key={key} value={key} title={label} />
                                ))
                            ) : (
                                <>
                                    <Option value="teacher_to_student" title="Giáo viên gửi học sinh" />
                                    <Option value="head_to_teacher" title="Tổ trưởng gửi giáo viên" />
                                    <Option value="principal_to_teacher" title="Hiệu trưởng gửi giáo viên" />
                                    <Option value="admin_to_all" title="Ban giám hiệu gửi toàn trường" />
                                </>
                            )}
                        </Select>
                    )}

                    {/* Chọn lớp học - chỉ hiển thị khi type là teacher_to_student */}
                    {formData.type === 'teacher_to_student' && (
                        <>
                            <Text style={{ marginBottom: '5px' }}>Chọn lớp học</Text>
                            {loadingClasses ? (
                                <Box style={{ display: 'flex', justifyContent: 'center', padding: '10px' }}>
                                    <LoadingIndicator />
                                </Box>
                            ) : (
                                <Select
                                    placeholder="Chọn lớp học"
                                    value={formData.recipients.class}
                                    onChange={(value) => {
                                        handleFormChange('class', value);
                                    }}
                                    style={{ marginBottom: '15px' }}
                                >
                                    {availableClasses.length > 0 ? (
                                        availableClasses.map((cls) => (
                                            <Option key={cls.id} value={cls.id} title={`${cls.name} - ${cls.classRoom}`} />
                                        ))
                                    ) : (
                                        <Option value="10A1" title="10A1 - Toán học" />
                                    )}
                                </Select>
                            )}
                        </>
                    )}

                    {/* Chọn bộ môn - chỉ hiển thị khi type là head_to_teacher */}
                    {formData.type === 'head_to_teacher' && (
                        <>
                            <Text style={{ marginBottom: '5px' }}>Chọn bộ môn</Text>
                            <Select
                                placeholder="Chọn bộ môn"
                                value={formData.recipients.department}
                                onChange={(value) => {
                                    handleFormChange('department', value);
                                }}
                                style={{ marginBottom: '15px' }}
                            >
                                {announcementConfig && announcementConfig.departments ? (
                                    announcementConfig.departments.map((dept) => (
                                        <Option key={dept} value={dept} title={dept} />
                                    ))
                                ) : (
                                    <>
                                        <Option value="Toán" title="Toán" />
                                        <Option value="Văn" title="Văn" />
                                        <Option value="Anh" title="Anh" />
                                        <Option value="Lý" title="Lý" />
                                        <Option value="Hóa" title="Hóa" />
                                        <Option value="Sinh" title="Sinh" />
                                        <Option value="Sử" title="Sử" />
                                        <Option value="Địa" title="Địa" />
                                        <Option value="GDCD" title="GDCD" />
                                    </>
                                )}
                            </Select>
                        </>
                    )}
                    {/* Tiêu đề thông báo */}
                    <Text style={{ marginBottom: '5px' }}>Tiêu đề thông báo</Text>
                    <Input
                        type="text"
                        placeholder="Nhập tiêu đề thông báo"
                        value={formData.title}
                        onChange={(e) => handleFormChange('title', e.target.value)}
                        style={{ marginBottom: '15px' }}
                    />
                    {/* Nội dung thông báo */}
                    <Text style={{ marginBottom: '5px' }}>Nội dung thông báo</Text>
                    <Input
                        type="textarea"
                        placeholder="Nhập nội dung thông báo"
                        value={formData.content}
                        onChange={(e) => handleFormChange('content', e.target.value)}
                        style={{ marginBottom: '15px', minHeight: '100px' }}
                    />
                    {/* Gửi qua Zalo */}
                    <Checkbox
                        checked={formData.zaloConfig.enabled}
                        onChange={(e) => handleFormChange('zaloEnabled', e.target.checked)}
                        style={{ marginBottom: '15px' }}
                    >
                        Gửi qua Zalo
                    </Checkbox>
                    {formData.zaloConfig.enabled && (
                        <>
                            {/* Chọn nhóm Zalo */}
                            <Text style={{ marginBottom: '5px' }}>Chọn nhóm Zalo</Text>
                            {loadingZaloGroups ? (
                                <Box style={{ display: 'flex', justifyContent: 'center', padding: '10px' }}>
                                    <LoadingIndicator />
                                </Box>
                            ) : (
                                <Select
                                    placeholder="Chọn nhóm Zalo"
                                    value={formData.zaloConfig.groupId}
                                    onChange={(value) => {
                                        handleFormChange('groupId', value);
                                    }}
                                    style={{ marginBottom: '15px' }}
                                >
                                    {zaloGroups.length > 0 ? (
                                        zaloGroups.map((group) => {
                                            const groupId = user.role === 'admin' ? group.group_id : group.groupId;
                                            const groupName = user.role === 'admin' ? group.name : group.groupName;
                                            return (
                                                <Option key={groupId} value={groupId} title={groupName} />
                                            );
                                        })
                                    ) : (
                                        <Option value="sample_group" title="Nhóm Zalo mẫu" />
                                    )}
                                </Select>
                            )}
                            {/* Chọn thời gian lên lịch */}
                            <Text style={{ marginBottom: '5px' }}>Thời gian gửi (tùy chọn)</Text>
                            <Input
                                type="datetime-local"
                                placeholder="Chọn thời gian gửi (tùy chọn)"
                                value={formData.zaloConfig.scheduledTime}
                                onChange={(e) => handleFormChange('scheduledTime', e.target.value)}
                                style={{ marginBottom: '15px' }}
                            />
                        </>
                    )}
                </Box>
            </Modal>
            {/* NewsComponent */}
            <NewsComponent />
            {/* Bottom Navigation */}
            <BottomNavigationEdu />
            {/* Announcement Detail Modal */}
            <Modal
                visible={announcementDetailVisible}
                title="Chi tiết thông báo"
                onClose={() => {
                    setAnnouncementDetailVisible(false);
                    setSelectedAnnouncement(null);
                }}
                actions={[
                    { text: 'Đóng', close: true }
                ]}
            >
                {selectedAnnouncement && (
                    <Box p={4}>
                        <Box style={{ marginBottom: '15px' }}>
                            <Text bold size="large" style={{ marginBottom: '8px' }}>
                                {selectedAnnouncement.title || 'Thông báo'}
                            </Text>
                            <Box flex alignItems="center" style={{ marginBottom: '10px' }}>
                                <Text style={{ fontSize: '14px', color: '#666' }}>
                                    Từ: <Text bold>{selectedAnnouncement.sender?.name}</Text>
                                </Text>
                                <Text style={{ fontSize: '14px', color: '#666', marginLeft: '15px' }}>
                                    {formatDistanceToNow(new Date(selectedAnnouncement.createdAt), { locale: vi, addSuffix: true })}
                                </Text>
                            </Box>
                            {selectedAnnouncement.type && (
                                <Box style={{
                                    backgroundColor: '#e8f0fe',
                                    color: '#0068ff',
                                    padding: '4px 8px',
                                    borderRadius: '4px',
                                    fontSize: '12px',
                                    display: 'inline-block',
                                    marginBottom: '10px'
                                }}>
                                    {selectedAnnouncement.type === 'teacher_to_student' && 'Giáo viên gửi học sinh'}
                                    {selectedAnnouncement.type === 'head_to_teacher' && 'Tổ trưởng gửi giáo viên'}
                                    {selectedAnnouncement.type === 'principal_to_teacher' && 'Hiệu trưởng gửi giáo viên'}
                                    {selectedAnnouncement.type === 'admin_to_all' && 'Ban giám hiệu gửi toàn trường'}
                                </Box>
                            )}
                        </Box>
                        <Box style={{
                            backgroundColor: '#f9f9f9',
                            padding: '15px',
                            borderRadius: '8px',
                            lineHeight: 1.6
                        }}>
                            <Text>{selectedAnnouncement.content}</Text>
                        </Box>
                        {selectedAnnouncement.zaloConfig?.enabled && (
                            <Box style={{ marginTop: '15px', padding: '10px', backgroundColor: '#f0f8ff', borderRadius: '6px' }}>
                                <Text style={{ fontSize: '12px', color: '#666' }}>
                                    📱 Đã gửi qua Zalo: {selectedAnnouncement.zaloConfig.groupName}
                                    {selectedAnnouncement.zaloConfig.sentAt && (
                                        <Text> • {formatDistanceToNow(new Date(selectedAnnouncement.zaloConfig.sentAt), { locale: vi, addSuffix: true })}</Text>
                                    )}
                                </Text>
                            </Box>
                        )}
                    </Box>
                )}
            </Modal>

            {/* Confirm Modal */}
            <ConfirmModal
                visible={confirmModal.visible}
                title={confirmModal.title}
                message={confirmModal.message}
                onConfirm={confirmModal.onConfirm}
                onCancel={closeConfirmModal}
                confirmText={confirmModal.confirmText}
                cancelText={confirmModal.cancelText}
                loading={submitting}
            />
            {/* Floating Action Button */}
            <Button
                className="add-button"
                style={{
                    position: 'fixed',
                    bottom: '70px',
                    right: '20px',
                    width: '56px',
                    height: '56px',
                    borderRadius: '28px',
                    backgroundColor: '#0068ff',
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '24px',
                    boxShadow: '0 2px 10px rgba(0, 104, 255, 0.3)',
                    zIndex: 100,
                }}
                onClick={() => setModalVisible(true)}
            >
                +
            </Button>
        </Box>
    );
};

export default TeacherEdu;